@extends('seller.admin_template') @section('pageTitle', 'Create Product') @section('content')

<script src="{{ asset ("assets/js/plugins/forms/styling/switchery.min.js ") }}"></script>
<script src="{{ asset ("assets/js/plugins/forms/styling/switch.min.js ") }}"></script>

@include('seller.notification')

<style>
    .padding-zero {
        padding: 0px;
        padding-right: 5px;
    }

    .text-end {
        text-align: end;
    }
    legend {
        padding-bottom:5px;
    }
</style>
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- 2 columns form -->
<form class="form-horizontal" action="/seller/user" method="POST" enctype="multipart/form-data">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h5 class="panel-title">Add Sub User</h5>
            <div class="heading-elements">
            </div>
        </div>

        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <fieldset>
                        <legend class="text-semibold"><b><i class="icon-file-text position-left"></i>Sub User Details</b></legend>

                        <div class="form-group">
                            <label class="col-lg-3 control-label">Name: <span style="color:red">*</span></label>
                            <div class="col-lg-9">
                                <input type="text" class="form-control" maxLength="60" id="full_name" placeholder="e.g: Ahmed" name="full_name" required data-validation-required-message="Please enter User name">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-3 control-label">Phone: <span style="color:red">*</span></label>
                            <div class="col-lg-9">
                                <input type="text" class="form-control" maxLength="11" placeholder="e.g: 03331234567" name="phone" pattern="^\d{11}$" required data-validation-required-message="Please enter Phone No">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-3 control-label">CNIC:</label>
                            <div class="col-lg-9">
                                <input type="text" class="form-control" maxLength="20" placeholder="e.g: 42321-1234567-1" name="cnic" >
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-3 control-label">Email: <span style="color:red">*</span></label>
                            <div class="col-lg-9">
                                <input type="email" class="form-control" maxLength="60" placeholder="e.g: <EMAIL>" name="email" required data-validation-required-message="Please enter Email">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-3 control-label">Password: <span style="color:red">*</span></label>
                            <div class="col-lg-9">
                                <input type="text" class="form-control"  minlength=8 placeholder="e.g: Ahmed@@11" name="password" required data-validation-required-message="Please enter Password">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-3">Enable/Disable:</label>
                            <div class="col-lg-9">
                                <input type="checkbox" value=1 class="switchery-primary" name="activated" checked>
                            </div>
                        </div>

                        @php($add_on_location = \App\Models\AddOn::location(Auth::id()))
                            
                            <div class="form-group">
                                <label style="padding-top:13px" class="col-lg-3">Filter Orders {{ $add_on_location ? '' : ' and Shipments ' }} by Destination Cities:</label>
                                <div class="col-lg-9">
                                    <select data-placeholder="Select a City" name="cities[]" multiple="multiple" class="select-remote-data">
                                    </select>
                                    <span style="color:red">Note : Left Empty if you want your user to see all <b>Destination Cities</b> orders {{ $add_on_location ? '' : ' and shipments ' }} </span>
                                </div>
                            </div>

                            @if ($add_on_location)
                                <div class="form-group">
                                    <label style="padding-top:13px" class="col-lg-3">Filter Shipments by Pickup Location(s):</label>
                                    <div class="col-lg-9">
                                        <select data-placeholder="Select a Pickup Location" name="locations[]" multiple="multiple" class="select">
                                            @foreach ($locations as $location)
                                                <option value={{$location->id}}>{{$location->location_name}}</option>
                                            @endforeach
                                        </select>
                                        <span style="color:red">Note : Left Empty if you want your user to see all <b>Pickup Location</b> shipments</span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label style="padding-top:13px" class="col-lg-3">Filter Orders by Pickup Location(s):</label>
                                    <div class="col-lg-9">
                                        <select data-placeholder="Select a Pickup Location" name="order_pickup_locations[]" multiple="multiple" class="select">
                                            @foreach ($locations as $location)
                                                <option value={{$location->id}}>{{$location->location_name}}</option>
                                            @endforeach
                                        </select>
                                        <span style="color:red">Note : Left Empty if you want your user to see all <b>Pickup Locations</b> orders </span>
                                    </div>
                                </div>
                            @endif

                            

                    </fieldset>
                </div>

                <div class="col-md-6">

                    <fieldset>
                        <legend class="text-semibold">
                            <b>
                                <i class="icon-stairs position-left"></i>
                                Sub User Permissions
                            </b>
                            <input type="checkbox" class="switchery" id="all_checkbox" checked>
                        </legend>
                        
                        {{-- Order Permissions --}}
                        <legend class="text-semibold">
                            <b>Order </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('order', this)" checked>    
                        </legend>
 
                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">View:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox"  value=1 name="order_view"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Create:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox"  value=1 name="order_create"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Edit:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                   <input type="checkbox" class="switchery order_checkbox"  value=1 name="order_edit" id="order_edit_checkbox" onchange="toggleCheckbox('order_edit', this)"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Cancel:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox"  value=1 name="order_cancel"  checked>    
                                </div>
                            </div>
                            
                        </div>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Process:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox"  value=1 name="order_process"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Bulk Cancel:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox"  value=1 name="bulk_order_cancellation"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Dump Report:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox" value=1 name="order_data_dump_report"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Robocall:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox" value=1 name="robocall"  checked>    
                                </div>
                            </div>

                        </div>

                        <div class="form-group">
                            <div class="col-md-3">
                            <label class="col-lg-9 padding-zero">Comments:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox"  value=1 name="order_comments"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Tagging:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_checkbox"  value=1 name="order_tagging"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zer text-end">COD Switch:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery order_edit_checkbox"  value=1 name="cod_switch" id="cod_switch" checked>    
                                </div>
                            </div>

                         </div>

 
 
                        {{-- Shipment Permissions --}}
                        <legend class="text-semibold">
                            <b>Shipment </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('shipment', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">View:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery shipment_checkbox"  value=1 name="shipment_view"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Cancel:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery shipment_checkbox"  value=1 name="shipment_cancel"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Bulk Cancel:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery shipment_checkbox"  value=1 name="bulk_shipment_cancellation"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Auto Booking:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery shipment_checkbox"  value=1 name="auto_shipped"  checked>    
                                </div>
                            </div>

                        </div>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Loadsheet:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery shipment_checkbox"  value=1 name="loadsheet"  checked>    
                                </div>
                            </div>

                        </div>

 
 
                        {{-- Reverse - Shipment Permissions --}}
                        <legend class="text-semibold">
                            <b>Reverse Shipment </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('reverse_shipment', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">View:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery reverse_shipment_checkbox" value=1 name="view_reverse_shipment"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Request:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery reverse_shipment_checkbox" value=1 name="rma_request"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Setting:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery reverse_shipment_checkbox" value=1 name="rma_settings"  checked>    
                                </div>
                            </div>

                        </div>



 
                        {{-- Others Permissions --}}
                        <legend class="text-semibold">
                            <b>Others </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('other', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Return Receiving:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery other_checkbox" value=1 name="return_received"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Setting:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery other_checkbox" value=1 name="setting"  checked>    
                                </div>
                            </div>

                        </div>



 
                        {{-- Print Permissions --}}
                        <legend class="text-semibold">
                            <b>Print </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('print', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Address Label:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery print_checkbox" value=1 name="print_address_label"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Shipping Invoice:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery print_checkbox" value=1 name="print_shipping_invoice"  checked>    
                                </div>
                            </div>

                        </div>

 
 
                        {{-- Product Permissions --}}
                        <legend class="text-semibold">
                            <b>Product </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('product', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">View:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery product_checkbox"  value=1 name="product_view"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Create:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery product_checkbox"  value=1 name="product_create"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Edit:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery product_checkbox"  value=1 name="product_edit"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Delete:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery product_checkbox"  value=1 name="product_delete"  checked>    
                                </div>
                            </div>

                        </div>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Conflict View:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery product_checkbox"  value=1 name="product_conflict_view" checked>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">View Storefront ID:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery product_checkbox"  value=1 name="product_view_marketplace_id" checked>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Edit Storefront ID:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery product_checkbox"  value=1 name="product_edit_marketplace_id" checked>
                                </div>
                            </div>

                        </div>

 
 
                        {{-- Carton Permissions --}}
                        <legend class="text-semibold">
                            <b>Carton Management</b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('carton', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">View:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery carton_checkbox"  value=1 name="carton_view"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Create:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery carton_checkbox"  value=1 name="carton_create"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Edit:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery carton_checkbox"  value=1 name="carton_edit"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Delete:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery carton_checkbox"  value=1 name="carton_delete"  checked>    
                                </div>
                            </div>

                        </div>



 
                        {{-- Reports Permissions --}}
                        <legend class="text-semibold">
                            <b>Reports </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('report', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Reports:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery report_checkbox" value=1 name="reports"  checked>    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">COD Reconcile:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery report_checkbox" value=1 name="cod_reconciliation"  checked>    
                                </div>
                            </div>

                        </div>

                        

                        @if ($add_ons['ffc'])


                            {{-- Fulfillment Order Permissions --}}
                            <legend class="text-semibold">
                                <b>Fulfillment Order </b>
                                <input type="checkbox" class="switchery" onchange="toggleCheckbox('fo', this)" checked>
                            </legend>

                            <div class="form-group">

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">Edit:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery fo_checkbox" value=1 name="fo_edit">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Reports:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery fo_checkbox" value=1 name="fo_report">    
                                    </div>
                                </div>

                            </div>

                            {{-- Stock Order Permissions --}}
                            <legend class="text-semibold">
                                <b>Stock Order </b>
                                <input type="checkbox" class="switchery" onchange="toggleCheckbox('so', this)" checked>
                            </legend>

                            <div class="form-group">

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">View:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery so_checkbox"  checked value=1 name="so_view">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Create: </label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery so_checkbox"   checked  value=1 name="so_create">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Cancel: </label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery so_checkbox" checked value=1 name="so_cancel">    
                                    </div>
                                </div>

                            </div>



                            
                            {{-- Fulfillment Centre Permissions --}}
                            <legend class="text-semibold">
                                <b>Fulfillment Centre (FFC) </b>
                                <input type="checkbox" class="switchery" onchange="toggleCheckbox('ffc', this)" checked>
                            </legend>

                            <div class="form-group">

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">Location View:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_checkbox"  checked  value=1 name="ffc_location_view">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Location Create:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_checkbox"  checked  value=1 name="ffc_location_create">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Location Update:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_checkbox"  checked  value=1 name="ffc_location_update">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Inventory View:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_checkbox"  checked  value=1 name="ffc_inventory_view">    
                                    </div>
                                </div>

                            </div>

                            <div class="form-group">

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">Inventory Dump:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_checkbox"   checked  value=1 name="ffc_inventory_dump_view">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">Inventory Bulk Upload:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_checkbox" checked  value=1 name="inventory_bulk_upload">    
                                    </div>
                                </div>

                            </div>


                            {{-- Fulfillment Centre App Permissions --}}
                            <legend class="text-semibold">
                                <b>Fulfillment Centre (FFC) App </b>
                                <input type="checkbox" class="switchery" onchange="toggleCheckbox('ffc_app', this)" checked>
                            </legend>

                            <div class="form-group">

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">Login:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_app_checkbox"  checked  value=1 name="ffc_app_login">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Security Scan: </label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_app_checkbox"  checked  value=1 name="ffc_app_security">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Putaway Inbound:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_app_checkbox"  checked  value=1 name="ffc_app_putaway_inbound">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Picking: </label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_app_checkbox"  checked  value=1 name="ffc_app_picking">    
                                    </div>
                                </div>

                            </div>



                            <div class="form-group">

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">Item Counting Mode Switcher:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery ffc_app_checkbox" checked value=1 name="ffc_app_item_counting_mode">    
                                    </div>
                                </div>

                            </div>



                            
                            {{-- Packing Desk Permissions --}}
                            <legend class="text-semibold">
                                <b>Packing Desk </b>
                                <input type="checkbox" class="switchery" onchange="toggleCheckbox('packing_desk', this)" checked>
                            </legend>

                            <div class="form-group">

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero">View:</label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery packing_desk_checkbox"  checked  value=1 name="packing_desk_view">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Pack: </label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery packing_desk_checkbox"  checked  value=1 name="packing_desk_pack">    
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label class="col-lg-9 padding-zero text-end">Release: </label>
                                    <div class="col-lg-3 checkbox-switchery switchery-sm">
                                        <input type="checkbox" class="switchery packing_desk_checkbox"  checked  value=1 name="packing_desk_release">    
                                    </div>
                                </div>

                            </div>


                        @endif

                        
                        {{-- Email Permissions --}}
                        <legend class="text-semibold">
                            <b>Email </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('email', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Technical Support:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery email_checkbox"  checked  value=1 name="email_technical_support">    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Shipper Advice: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery email_checkbox"  checked  value=1 name="email_shipper_advice">    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Daily Summary: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery email_checkbox"  checked  value=1 name="email_daily_summary">    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Order Data Dump Report: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery email_checkbox"  checked  value=1 name="email_order_data_dump_report">    
                                </div>
                            </div>

                        </div>



                        
                        {{-- Data Privacy Permissions --}}
                        <legend class="text-semibold">
                            <b>Data Privacy </b>
                            <input type="checkbox" class="switchery"  id="data_privacy_checkbox_main"  onchange="toggleCheckbox('data_privacy', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">View PII:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery data_privacy_checkbox" value=1 name="data_privacy_view_pii" checked>    
                                </div>
                            </div>

                        </div>




                        @if ($add_ons['scan-n-ship'])
                            {{-- Scan N Ship Permissions --}}
                            <legend class="text-semibold">
                                <b>Scan N Ship </b>
                                <input type="checkbox" class="switchery" value=1 name="scan_n_ship" >
                            </legend>
                        @endif

                        @if ($add_ons['gtech'])

                        <legend class="text-semibold">
                            <b>Gtech Re-syncing </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('gtech_resyncing', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Transfer Order:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery gtech_resyncing_checkbox"  checked  value=1 name="gtech_transfer_order">    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Sales Order: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery gtech_resyncing_checkbox"  checked  value=1 name="gtech_sales_order">    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Cancellation Re-sync: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery gtech_resyncing_checkbox"  checked  value=1 name="gtech_cancellation_resync">    
                                </div>
                            </div>

                            <!-- <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Gtech Inventory Fetch: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery gtech_resyncing_checkbox"  checked  value=1 name="gtech_inventory_fetch">    
                                </div>
                            </div> -->

                        </div>

                        @endif

                        @if ($add_on_erp_jdot || $add_on_erp_jdot_temp)
                        <legend class="text-semibold">
                            <b>ERP resync Permissions</b>
                            <input type="checkbox" class="switchery" id="erp_jdot_resyncing_checkbox_main" onchange="toggleCheckbox('erp_jdot_resyncing', this)" checked>
                        </legend>
                        <div class="form-group">
                            <div class="col-md-4">
                                <label class="col-lg-9 padding-zero">ERP Sales Order:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery erp_jdot_resyncing_checkbox" value=1 name="erp_jdot_sales_order" checked>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="col-lg-9 padding-zero">ERP Return Order:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery erp_jdot_resyncing_checkbox" value=1 name="erp_jdot_return_order" checked>
                                </div>
                            </div>
                        </div>
                        @endif
                        @if ($add_ons['erp-technosys'])

                        <legend class="text-semibold">
                            <b>Technosys Re-syncing </b>
                            <input type="checkbox" class="switchery" onchange="toggleCheckbox('technosys_resyncing', this)" checked>
                        </legend>

                        <div class="form-group">

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero">Transfer Order:</label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery technosys_resyncing_checkbox"  checked  value=1 name="technosys_transfer_order">    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Sales Order: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery technosys_resyncing_checkbox"  checked  value=1 name="technosys_sales_order">    
                                </div>
                            </div>

                            <div class="col-md-3">
                                <label class="col-lg-9 padding-zero text-end">Cancellation Re-sync: </label>
                                <div class="col-lg-3 checkbox-switchery switchery-sm">
                                    <input type="checkbox" class="switchery technosys_resyncing_checkbox"  checked  value=1 name="technosys_cancellation_resync">    
                                </div>
                            </div>


                        </div>

                        @endif


                    </fieldset>

                </div>
                {{ csrf_field() }}
            </div>

            <div class="text-right">
                <button type="submit" class="btn btn-primary">Add <i class="icon-arrow-right14 position-right"></i></button>
            </div>
        </div>
    </div>


</form>
<!-- /2 columns form -->


 

<script>
    $(function() {


        $(".select-remote-data").select2({
            ajax: {
                url: "/seller/cities/search",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        string: params.term, // search term
                    };
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.name+" | "+item.country_code,
                                id: item.id
                            }
                        })
                    };
                },
                cache: true
            },
            minimumInputLength: 1
        });

        $('.select').select2({
            minimumResultsForSearch: Infinity
        });


        // Switchery
        // ------------------------------

        // Initialize multiple switches
        if (Array.prototype.forEach) {
            var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
            elems.forEach(function(html) {
                var switchery = new Switchery(html);
                html.switcheryInstance = switchery; // Attach instance to the DOM element
            });
        }
        else {
            var elems = document.querySelectorAll('.switchery');
            for (var i = 0; i < elems.length; i++) {
                var switchery = new Switchery(elems[i]);
            }
        }

        // Colored switches
        var primary = document.querySelector('.switchery-primary');
        var switchery = new Switchery(primary, { color: '#2196F3' });
    });




    /// Toggle all Checkboxes
    $('#all_checkbox').on('change',function() {
        if ($(this).is(':checked')) {
            $(".switchery").prop('checked', true).trigger("click");
        } else {
            $(".switchery").prop('checked', false).trigger("click");
		}
	});


    /// Toggle each module Checkboxes
    function toggleCheckbox(param, element) {

        if (param === "order_edit") {
            const codSwitch = document.getElementById('cod_switch');
            if (codSwitch) {
                const switcheryInstance = codSwitch.switcheryInstance;
                if (switcheryInstance) {
                    setTimeout(() => {
                        $(element).is(':checked')
                            ? switcheryInstance.enable()
                            : switcheryInstance.disable();
                    }, 50); // Delay for proper DOM handling
                }
            }
        }

        if ($(element).is(':checked')) {
            $(`.${param}_checkbox`).prop('checked', false).trigger("click");
        } else {
            $(`.${param}_checkbox`).prop('checked', true).trigger("click");
		}
    }


</script>

@endsection @section('scripts') @endsection