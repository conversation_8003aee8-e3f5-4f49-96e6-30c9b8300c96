@extends('seller.admin_template')

@section('pageTitle', 'Orders')

@section('content')


    {{-- Scripts --}}
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/col_reorder.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/select.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js") }}"></script>

	<script src="{{ asset ("assets/js/pages/form_layouts.js ") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/pickers/daterangepicker.js") }}"></script>
    {{-- Scripts End --}}

    {{-- Custom CSS --}}
    <style>
        .error-row {
            background-color: #ffcdd2 !important; /* Very light red background */
        }

        .error-row:hover {
            background-color: #ffc3c9 !important; /* Slightly darker red on hover */
        }

        .error-row td {
            background-color: transparent !important; /* Ensure cells inherit the row background */
        }


        .warning-row {
            background-color: #fff7cd !important; /* Very light red background */
        }

        .warning-row:hover {
            background-color: #fef3b9 !important; /* Slightly darker red on hover */
        }

        .warning-row td {
            background-color: transparent !important; /* Ensure cells inherit the row background */
        }
    </style>




    {{-- Content --}}
    <div class="row">

        <div class="col-lg-4">
            <div class="info-panel-popover panel has-bg-image"data-popup="popover" title="<b>Total Cartons</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Total Cartons.">
                <div class="panel-body">
                <h1 style="line-height: 0.90;" class="no-margin"><b> @formatInteger($summary['total']) </b><span style="font-size:14px"> Total Cartons</span></h1>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="success-panel-popover panel has-bg-image"  data-popup="popover" title="<b>Total Active Cartons</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Total Active Cartons.">
                <div class="panel-body">
                    <h1 style="line-height: 0.90;" class="no-margin"><b> @formatInteger( $summary['active'] ) </b><span style="font-size:14px"> Active Cartons</span></h1>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="danger-panel-popover panel has-bg-image"  data-popup="popover" title="<b>Total In-Active Cartons</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Total In-Active Cartons.">
                <div class="panel-body">
                    <h1 style="line-height: 0.90;" class="no-margin"><b> @formatInteger( $summary['inactive'] ) </b><span style="font-size:14px"> In Active Cartons</span></h1>
                </div>
            </div>
        </div>

    </div>

    @include('seller.notification')


    <!-- Page length options -->
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h5 class="panel-title"><i class="icon-dropbox"></i>&nbsp {{$page_title}}</h5>
            <div class="heading-elements visible-elements">

                @if ( (!session()->has('permission') || session('permission')->carton_create))
                    <a class="btn btn-default" href="cartons/create"><i class="icon-dropbox"></i>&nbsp Create</a>
                @endif

            </div>
        </div>

        <div class="panel-body">

            <table id="main-table" class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Barcode</th>
                        <th>Total Products</th>
                        <th>Total Quantity</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Updated At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tfoot style="display: table-row-group;">
                    <tr>
                        <td>Name</td>
                        <td>Barcode</td>
                        <td>Total Products</td>
                        <td>Total Quantity</td>
                        <td id="tag">
                            <select id="tag" class="select">
                                <option value="">All</option>
                                <option value="1">Active</option>
                                <option value="0">In-Active</option>
                            </select>
                        </td>
                        
                        <td id="date" ><input type="text" id="date" class="form-control daterange-basic" placeholder="Enter Range"></td>
                        <td id="date" ><input type="text" id="date" class="form-control daterange-basic" placeholder="Enter Range"></td>
                        <td></td>


                    </tr>
                </tfoot>
            </table>

        </div>
    </div>
    <!-- /page length options -->

    {{-- Content --}}





<script>

	$.extend( $.fn.dataTable.defaults, {
		dom: '<"datatable-header"B><"datatable-scroll-wrap"rt><"datatable-footer"ip>',
		buttons: {
			buttons: [
				{extend: 'pageLength',className: 'btn bg-slate-600'},
				{
					extend: 'collection',
					text: 'Export',
					className: 'btn bg-indigo-400 btn-icon',
					buttons: [
						{extend: 'copyHtml5',text: '<i class="icon-copy3"></i>&nbsp &nbsp Copy ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						},
						{extend: 'csvHtml5',text: '<i class="icon-file-excel"></i>&nbsp &nbsp CSV ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						},
						{extend: 'pdfHtml5',text: '<i class="icon-printer2"></i>&nbsp &nbsp pdf ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						}
					]
				},
				{extend: 'colvis',columns: ':gt(1)',text: '<i class="icon-grid3"></i> <span class="caret"></span>',className: 'btn bg-indigo-400 btn-icon'}
			]
		},
		select: {
			style: 'os',
			selector: 'td:first-child'
		},
		colReorder: false,
		stateSave: true,
		scrollX: true,
		scrollY: '50vh',
		scrollCollapse: true,
		"deferLoading": true,
		"processing": true,
		"language": {"processing": '<i style="color:green;font-size:50px" class="icon-spinner4 spinner"></i>'}

	});

    Date.prototype.addDays = function(days) {
		var date = new Date(this.valueOf());
		date.setDate(date.getDate() + days);
		return date;
	}

    $('.daterange-basic').daterangepicker({
        opens: 'center',
        drops: 'up',
        applyClass: 'bg-slate-600',
        cancelClass: 'btn-default',
        locale: {
            format: 'YYYY/MM/DD'
        }
    });
    $('.daterange-basic').val('');	


    $('.daterange-basic').on('apply.daterangepicker', function(ev, picker) {
		orderPlacedSearch = true;
	});
	$('.daterange-basic').on('cancel.daterangepicker', function(ev, picker) {
		orderPlacedSearch = false;
		$(this).val('');
	});


    $('#main-table tfoot td').each(function () {
        let title = $(this).text();
        if (!$(this).attr('id') && title) {
            $(this).html('<input type="text" class="form-control input-sm" placeholder="Search '+title+'" />');
        }
    });

    var main_table = $('#main-table').DataTable( {
        lengthMenu: [
            [ 20, 30, 50, 100, 500, 2000, -1],
            [ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
        ],
        retrieve: true,
        "serverSide": true,
        "ajax": "/seller/cartons/all",
        columnDefs: [ {
            orderable: false,
            searchable: false,
            targets:   [7]
        }],
        columns: [
            
            { data: 'name', name: 'cartons.name', render: function(data, type, row) {
                    return ('<a target="_blank" href="cartons/'+row.id+'/edit">'+data+'</a>');
                }
            },
            { data: 'barcode', name: 'cartons.barcode' },
            { data: 'total_product', name: 'total_product' },
            { data: 'total_quantity', name: 'total_quantity' },
            { data: 'status', name: 'status', render: function(data, type, row) {
                    if(data == 1) {
                        return '<td><span class="label label-success">Active</span></td>'
                    } else if(data == 0) {
                        return '<td><span class="label label-danger">In-Active</span></td>'
                    }
                }
            },
            { data: 'created_at', name: 'created_at' },
            { data: 'updated_at', name: 'updated_at' },
            { render: function(data, type, row) {
                
                    return (
                        @if ( (!session()->has('permission') || session('permission')->carton_edit))
                            (row.status == 1 ?
                            '<a class="btn btn-danger btn-xs" onclick="changeStatus('+row.id+', 0)"><i class="icon-blocked"></i> In-Active</a> '
                            :
                            '<a class="btn btn-success btn-xs" onclick="changeStatus('+row.id+', 1)"><i class="icon-blocked"></i> Active</a> ')
                        @else
                            ' ' 
                        @endif
                        +
                        @if ( (!session()->has('permission') || session('permission')->carton_delete))
                            '<a class="btn btn-danger btn-xs" onclick="deleteCarton('+row.id+')"><i class="icon-trash"></i> Delete</a>'
                        @else
                            ' '
                        @endif
                    );
                }
            }
        ],
        "order": [[5, 'desc']]
    });
	main_table.columns().search( '' ).draw();


    main_table.columns().every( function () {
        let that = this;
        $('input', this.footer()).on('keyup change', function (e) {
            if (e.keyCode == 13 || $(this).attr('id')) {
                if ($(this).attr('id')) {
                    setTimeout(() => {
                        if (orderPlacedSearch) {
                            let start = new Date (((this.value).split('-'))[0]);
                            let end = new Date (((this.value).split('-'))[1]);
                            let dateArray = new Array();
                            while (start <= end) {
                                start = start.addDays(1);
                                dateArray.push((new Date (start)).toISOString().slice(0,10));
                            }
                            let range = dateArray.join('|');
                            that.search(range,true,false).draw();

                        } else {
                            that.search('',true,false).draw();
                        }
                        
                    }, 500);

                } else {
                    if((this.value).toLowerCase() == 'no location assigned'){
                        that.search('undefined',true,false).draw();
                    } else{

                        // that.search(((this.value).trim()).replace(/\|$/, ''),true,false).draw();
                        that.search(((this.value).trim()).replace(/ /g, '|'),true,false).draw();
                    }
                    
                }
            }
        });

        $('.select', this.footer()).on('change', function (e) {
            if (e.keyCode == 13 || $(this).attr('id')) {
                if ($(this).attr('id')) {
                    setTimeout(() => {
                        that.search(this.value,true,false).draw();
                    }, 500);
                }
            }
        });
    });


    /// For changing the status of a carton
    function changeStatus(carton_id, status)
    {
        if (!confirm('Are you sure you want to change the status of this carton ?')) {
            return;
        }

        $.ajax({
            url: '{{ route("cartons.toggle-status") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                status: 0, // 0 for inactive, 1 for active
                carton_id: carton_id
            },
            success: function(response) {

                if (response.success) {

                    alert(response.message);
                    main_table.ajax.reload();

                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Error performing action. Please try again.');
            }
        });
    }


    function deleteCarton(carton_id)
    {
        if (!confirm('Are you sure you want to delete this carton?')) {
            return;
        }

        $.ajax({
            url: '{{ route("cartons.destroy", ":carton") }}'.replace(':carton', carton_id),
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                alert(response.message);
                main_table.ajax.reload();
            },
            error: function(xhr) {
                alert('Error performing action. Please try again.');
            }
        });
    }


</script>
@endsection