@extends('seller.admin_template')

@section('pageTitle', 'Orders')

@section('content')

	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/col_reorder.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/select.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js") }}"></script>

	<script src="{{ asset ("assets/js/pages/form_layouts.js ") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/pickers/daterangepicker.js") }}"></script>

	{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js" integrity="sha512-qTXRIMyZIFb8iQcfjXWCO8+M5Tbc38Qi5WzdPOYZHIlZpzBHG3L3by84BBBOiRGiEb7KKtAOAs5qYdUiZiQNNQ==" crossorigin="anonymous"></script> --}}
	<script src="https://unpkg.com/libphonenumber-js@1.10.34/bundle/libphonenumber-min.js"></script>

	<style>
		.panel_main_text {
            font-size:75pt;
        }
		.panel_sub_text {
            font-size:21.37pt;
        }
		@media (max-width: 500px) {
        .panel_main_text {
            font-size:35pt;
        }
		.panel_sub_text {
            font-size:11.37pt;
        }
    }
	</style>
@php
    $public_order_link = url('public/order/') . '/' . base64_encode(Auth::user()->company_name);
@endphp

	<div class="row">
		<div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
			<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
				<div id="pending_div_1" class="panel has-bg-image"  data-popup="popover" title="<b>Pending</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Those orders whose shipment are not created yet.<br><b>Editable : </b> <i style='color:green' class='icon-check2'></i>.<br><b>Cancellable : </b> <i style='color:green' class='icon-check2'></i>.(Partially or Fully)"
				style="background-color: #253138;text-align: center;">
					<div class="panel-body" id="pending_div_2" >
						<span class="panel_main_text" style="color:#f0692c;" id="pending_div_3">{{$pending}}</span> <br><span class="panel_sub_text" style="color:#fff">  Pending </span>
					</div>
				</div>
			</div>
			<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
				<div id="processing_div_1" class="panel bg-blue-400  has-bg-image" data-popup="popover" title="<b>Processing</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Those orders whose shipment are created and their delivery is in progress.<br><b>Editable : </b> <i style='color:red' class='icon-cross'></i>.<br><b>Cancellable : </b> <i style='color:red' class='icon-cross'></i>.(Only Courier Can)"
				style="background-color: #253138;text-align: center;">
					<div class="panel-body" id="processing_div_2" >
					<span class="panel_main_text" style="color:#f0692c" id="processing_div_3">{{$processing}} </span> <br><span class="panel_sub_text" style="color:#fff">  Processing </span>
					</div>
				</div>
			</div>
		</div>
		<div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
			<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
				<div id="completed_div_1" class="panel bg-success-400  has-bg-image" data-popup="popover" title="<b>Completed</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Those orders whose atleast 1 order item is delivered and other items (if any) are either delivered or cancelled."
				style="background-color: #253138;text-align: center;">
					<div class="panel-body" id="completed_div_2" >
					<span class="panel_main_text" style="color:#f0692c" id="completed_div_3"> {{$completed}} </span><br> <span class="panel_sub_text" style="color:#fff">  Completed </span>
					</div>
				</div>
			</div>
			<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
				<div id="cancelled_div_1" class="panel bg-danger-400  has-bg-image" data-popup="popover" title="<b>Cancelled</b>" data-html="true" data-placement="bottom" data-trigger="hover" data-content="Those orders whose all order items are cancelled."
				style="background-color: #253138;text-align: center;">
					<div class="panel-body" id="cancelled_div_2" >
					<span class="panel_main_text" style="color:#f0692c" id="cancelled_div_3"> {{$cancelled}} </span><br> <span class="panel_sub_text" style="color:#fff">  Cancelled </span>
					</div>
				</div>
			</div>
		</div>

	</div>


			@include('seller.notification')

			<div class="row">
				<div class="col-md-3 col-sm-3 col-xs-3" id="all_filter_btn">
					<div class="form-group">
						<a class="btn btn-warning  all" style="display: block;background-color: #253138dc">All</a>
					</div>
				</div>
				<div class="col-md-3 col-sm-4 col-xs-4">
					<div class="form-group">
						<a class="btn btn-info  pending" style="display: block;">Pending & Processing</a>
					</div>
				</div>
				<div class="col-md-3 col-sm-4 col-xs-4">
					<div class="form-group">
						<a class="btn btn-success  fulfilled" style="display: block;">Completed</a>
					</div>
				</div>
				<div class="col-md-3 col-sm-4 col-xs-4">
					<div class="form-group">
						<a class="btn btn-danger  cancelled" style="display: block;">Cancelled</a>
					</div>
				</div>
			</div>


			<!-- Page length options -->
			<div class="panel panel-info" id="allTable">
				<div class="panel-heading">
					<h5 class="panel-title"><i class="icon-watch2"></i>&nbsp All</h5>
					<div class="heading-elements visible-elements">
						@if(session()->has('permission'))
							@if(session('permission')->order_create == 1)
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
							@endif
						@else
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
						@endif
					</div>
				</div>


				<div id="all_table_div">
					<table id="zeroTable" class="table">
						<thead>
							<tr>
								<th>Order ID</th>
								<th>Check</th>
								<th>Status</th>
								<th>Tag</th>

                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <th>Customer Name</th>
                                    <th>Customer Number</th>
                                    <th>Customer Address</th>
                                    <th>Likelihood</th>
                                    <th>Customer Email</th>

                                @else
                                    <th>Likelihood</th>
                                @endif

								
								<th>Order Placed</th>
								<th>Pickup Location</th>
								<th>Destination City</th>
								<th>Des. City Exist?</th>
								<th>Quarantine</th>
								<th>Return Ratio</th>
								<th>Country</th>
								<th>COD?</th>
								<th>Payment Method</th>
								<th>Grand Total</th>
								<th>Cancellation Reason</th>

							</tr>
						</thead>
						<tfoot style="display: table-row-group;">
							<tr>
								<td>Reference Order ID</td>
								<td></td>
								<td>Status</td>
								<td id="tag">
									<select id="tag" class="select">
										<option value="">All</option>
										<option value="untagged">Untagged</option>
										@foreach($tags as $tag)
											<option value="{{$tag->value}}">{{$tag->value}}</option>
										@endforeach
									</select>
								</td>

                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <td>Customer Name</td>
                                    <td>Customer Number</td>
                                    <td></td>
                                    <td id="likelihood">
                                        <select id="likelihood" class="select">
                                            <option value="">All</option>
                                            <option value="33">Low</option>
                                            <option value="66">Medium</option>
                                            <option value="99">High</option>
                                            <option value="0">Invalid</option>
                                            
                                        </select>
                                    </td>
                                    <td>Customer Email</td>

                                @else
                                
                                    <td id="likelihood">
                                        <select id="likelihood" class="select">
                                            <option value="">All</option>
                                            <option value="33">Low</option>
                                            <option value="66">Medium</option>
                                            <option value="99">High</option>
                                            <option value="0">Invalid</option>
                                            
                                        </select>
                                    </td>

                                @endif
								
								<td id="date" ><input type="text" id="date" class="form-control daterange-basic" placeholder="Enter Range"></td>
								<td>Pickup Location</td>
								<td>Destination City</td>
								<td></td>
								<td></td>
								<td></td>
								<td id="country_search">
									<select id="country-search" class="select">
										<option value="">All</option>
										<option value="PK">PK</option>
										<option value="^([^P].*|P[^K].*)$">Non-PK</option>
									</select>
								</td>
								<td id="cod_search">
									<select id="cod-search" class="select">
										<option value="">All</option>
										<option value="1">Yes</option>
										<option value="0">No</option>
									</select>
								</td>
								<td id="payment_method">
									<select id="payment_method" class="select">
										<option value="">All</option>
										@foreach($payment_methods as $payment_method)
											<option value="{{$payment_method->id}}">{{$payment_method->display_name}}</option>
										@endforeach
									</select>
								</td>
								<td>Grand Total</td>
								<td></td>


							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			<!-- /page length options -->

			<!-- Page length options -->
			<div class="panel panel-info" id="pendingTable">
				<div class="panel-heading">
					<h5 class="panel-title"><i class="icon-watch2"></i>&nbsp Pending</h5>
					<div class="heading-elements visible-elements">
						@if(session()->has('permission'))
							@if(session('permission')->order_create == 1)
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
							@endif
						@else
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
						@endif
					</div>
				</div>

				<div id="pending_table_mobile_div" hidden>
					<table id="firstTableMobile" class="table">
						<thead >
							<tr>
								
								<th></th>
								<th></th>
								
							</tr>
						</thead>
					
					</table>
				</div>

				<div id="pending_table_div">
					<table id="firstTable" class="table">
						<thead>
							<tr>
								<th>Order ID</th>
								<th>Check</th>
								<th>Status</th>
								<th>Tag</th>

                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <th>Customer Name</th>
                                    <th>Customer Number</th>
                                    <th>Customer Address</th>
                                    <th>Likelihood</th>
                                    <th>Customer Email</th>

                                @else
                                    <th>Likelihood</th>
                                @endif
								
								<th>Order Placed</th>
								<th>Pickup Location</th>
								<th>Destination City</th>
								<th>Des. City Exist?</th>
								<th>Country</th>
								<th>COD?</th>
								<th>Payment Method</th>
								<th>Grand Total</th>

								
								
							</tr>
						</thead>
						<tfoot style="display: table-row-group;">
							<tr>
								<td>Reference Order ID</td>
								<td></td>
								<td>Status</td>
								<td id="tag">
									<select id="tag" class="select">
										<option value="">All</option>
										<option value="untagged">Untagged</option>
										@foreach($tags as $tag)
											<option value="{{$tag->value}}">{{$tag->value}}</option>
										@endforeach
									</select>
								</td>

                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <td>Customer Name</td>
                                    <td>Customer Number</td>
                                    <td></td>
                                    <td id="likelihood">
                                        <select id="likelihood" class="select">
                                            <option value="">All</option>
                                            <option value="33">Low</option>
                                            <option value="66">Medium</option>
                                            <option value="99">High</option>
                                            <option value="0">Invalid</option>
                                            
                                        </select>
                                    </td>
                                    <td>Customer Email</td>

                                @else
                                
                                    <td id="likelihood">
                                        <select id="likelihood" class="select">
                                            <option value="">All</option>
                                            <option value="33">Low</option>
                                            <option value="66">Medium</option>
                                            <option value="99">High</option>
                                            <option value="0">Invalid</option>
                                            
                                        </select>
                                    </td>

                                @endif
								
								<td id="date" ><input type="text" id="date" class="form-control daterange-basic" placeholder="Enter Range"></td>
								<td>Pickup Location</td>
								<td>Destination City</td>
								<td></td>
								<td id="country_search">
									<select id="country-search" class="select">
										<option value="">All</option>
										<option value="PK">PK</option>
										<option value="^([^P].*|P[^K].*)$">Non-PK</option>
									</select>
								</td>
								<td id="cod_search">
									<select id="cod-search" class="select">
										<option value="">All</option>
										<option value="1">Yes</option>
										<option value="0">No</option>
									</select>
								</td>
								<td id="payment_method">
									<select id="payment_method" class="select">
										<option value="">All</option>
										@foreach($payment_methods as $payment_method)
											<option value="{{$payment_method->id}}">{{$payment_method->display_name}}</option>
										@endforeach
									</select>
								</td>
								<td>Grand Total</td>
								
								
							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			<!-- /page length options -->
			

			<!-- Page length options -->
			<div class="panel panel-success" id="fulfilledTable">
				<div class="panel-heading">
					<h5 class="panel-title"><i class="icon-check2"></i>&nbsp Fulfilled</h5>
					<div class="heading-elements visible-elements">
						@if(session()->has('permission'))
							@if(session('permission')->order_create == 1)
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
							@endif
						@else
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
						@endif
					</div>
				</div>

				<div id="fulfilled_table_mobile_div" hidden>
					<table id="fulfilledTableMobile" class="table">
						<thead >
							<tr>
								
								<th></th>
								<th></th>
								
							</tr>
						</thead>
					
					</table>
				</div>
				<div id="fulfilled_table_div">
					<table id="secondTable" class="table">
						<thead>
							<tr>
								<th>Order ID</th>
								<th>Check</th>
								<th>Status</th>
								<th>Tag</th>
                                
                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <th>Customer Name</th>
                                    <th>Customer Number</th>
                                    <th>Customer Address</th>
                                    <th>Customer Email</th>

                                @endif
								
								<th>Order Placed</th>
								<th>Pickup Location</th>
								<th>Destination City</th>
								
								<th>Country</th>
								
								
								<th>COD?</th>
								<th>Payment Method</th>
								<th>Grand Total</th>

								
							</tr>
						</thead>
						<tfoot style="display: table-row-group;">
							<tr>
								<td>Reference Order ID</td>
								<td></td>
								<td>Status</td>
								<td id="tag">
									<select id="tag" class="select">
										<option value="">All</option>
										<option value="untagged">Untagged</option>
										@foreach($tags as $tag)
											<option value="{{$tag->value}}">{{$tag->value}}</option>
										@endforeach
									</select>
								</td>
                                
                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <td>Customer Name</td>
                                    <td>Customer Number</td>
                                    <td></td>
                                    <td>Customer Email</td>

                                @endif
								
								<td id="date" ><input type="text" id="date" class="form-control daterange-basic" placeholder="Enter Range"></td>
								<td>Pickup Location</td>
								<td>Destination City</td>
								<td id="country_search">
									<select id="country-search" class="select">
										<option value="">All</option>
										<option value="PK">PK</option>
										<option value="^([^P].*|P[^K].*)$">Non-PK</option>
									</select>
								</td>
								<td id="cod_search">
									<select id="cod-search" class="select">
										<option value="">All</option>
										<option value="1">Yes</option>
										<option value="0">No</option>
									</select>
								</td>
								<td id="payment_method">
									<select id="payment_method" class="select">
										<option value="">All</option>
										@foreach($payment_methods as $payment_method)
											<option value="{{$payment_method->id}}">{{$payment_method->display_name}}</option>
										@endforeach
									</select>
								</td>
								<td>Grand Total</td>
								
							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			<!-- /page length options -->

			<!-- Page length options -->
			<div class="panel panel-danger" id="cancelledTable">
				<div class="panel-heading">
					<h5 class="panel-title"><i class="icon-cross"></i>&nbsp Cancelled</h5>
					<div class="heading-elements visible-elements">
						@if(session()->has('permission'))
							@if(session('permission')->order_create == 1)
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
							@endif
						@else
								<a class="btn btn-default" href="order/create"><i class="icon-cart"></i>&nbsp Create Order</a>
						@endif
					</div>
				</div>

				<div id="cancelled_table_mobile_div" hidden>
					<table id="cancelledTableMobile" class="table">
						<thead >
							<tr>
								
								<th></th>
								<th></th>
								
							</tr>
						</thead>
					
					</table>
				</div>

				<div id="cancelled_table_div">
					<table id="thirdTable" class="table">
						<thead>
							<tr>
								<th>Order ID</th>
								<th>Check</th>
								<th>Status</th>
								<th>Tag</th>
                                
                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <th>Customer Name</th>
                                    <th>Customer Number</th>
                                    <th>Customer Address</th>
                                    <th>Customer Email</th>

                                @endif
								
								<th>Order Placed</th>
								<th>Pickup Location</th>
								<th>Destination City</th>
								
								<th>Country</th>
								
								
								<th>COD?</th>
								<th>Payment Method</th>
								<th>Grand Total</th>
								
								<th>Reason</th>
								<th>Cancelled At</th>

							</tr>
						</thead>
						<tfoot style="display: table-row-group;">
							<tr>
								<td>Reference Order ID</td>
								<td></td>
								<td>Status</td>
								<td id="tag">
									<select id="tag" class="select">
										<option value="">All</option>
										<option value="untagged">Untagged</option>
										@foreach($tags as $tag)
											<option value="{{$tag->value}}">{{$tag->value}}</option>
										@endforeach
									</select>
								</td>

                                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                                
                                    <td>Customer Name</td>
                                    <td>Customer Number</td>
                                    <td></td>
                                    <td>Customer Email</td>

                                @endif
								
								<td id="date" ><input type="text" id="date" class="form-control daterange-basic" placeholder="Enter Range"></td>
								<td>Pickup Location</td>
								<td>Destination City</td>
								<td id="country_search">
									<select id="country-search" class="select">
										<option value="">All</option>
										<option value="PK">PK</option>
										<option value="^([^P].*|P[^K].*)$">Non-PK</option>
									</select>
								</td>
								<td id="cod_search">
									<select id="cod-search" class="select">
										<option value="">All</option>
										<option value="1">Yes</option>
										<option value="0">No</option>
									</select>
								</td>
								<td id="payment_method">
									<select id="payment_method" class="select">
										<option value="">All</option>
										@foreach($payment_methods as $payment_method)
											<option value="{{$payment_method->id}}">{{$payment_method->display_name}}</option>
										@endforeach
									</select>
								</td>
								<td>Grand Total</td>
								
								<td></td>
								<td></td>

							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			<!-- /page length options -->







			<!-- Mini modal -->
			<div id="addTagOrder" class="modal fade">
				<div class="modal-dialog">
					<div class="modal-content">
						<form action="order/tagAdd/multiple" method="POST" id="multipleTagForm" enctype="multipart/form-data">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal">&times;</button>
								<h5 class="modal-title">Assign Tag To Multiple Orders</h5>
							</div>
							{{ csrf_field() }}

							<div class="modal-body">
								<h6 class="text-semibold">Orders</h6>
								<p id="modalSelectedOrder"></p>

								<hr>

								<h6 class="text-semibold">Select Tag</h6>
								<div class="form-group">
									<div class="col-lg-12">
										<input type="hidden" id="selectedOrder" name="order">

										@foreach($tags as $tag)
										<div class="col-lg-6">
											<label class="radio-inline">
												<input type="radio" name="tag" value="{{$tag->id}}" required class="styled">
												<span class="label {{ $tag->color }}">{{ $tag->value }}</span>
											</label>
										</div>
										@endforeach

									</div>
								</div>
							</div>
							<br>

							<div class="modal-footer">
								<button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
								<button type="submit" id="multipleTagSubmitButton" class="btn btn-primary">Update Orders</button>
							</div>
						</form>
					</div>
				</div>
			</div>
			<!-- /mini modal -->





			<!-- Hidden Form For Order tag Remove -->
			<form style="display: hidden" action="order/tagRemove/multiple" method="POST" id="tagRemoveForm">
				{!! csrf_field() !!}
				<input type="hidden" id="selectedOrderRemove" name="order"/>
			</form>
			<!-- Hidden Form For Order tag Remove -->

			<!-- Hidden Form For Order Resend to DOM -->
			<form style="display: hidden" action="order/reSendToDOM/multiple" method="POST" id="reSendToDOMForm">
				{!! csrf_field() !!}
				<input type="hidden" id="selectedOrderToResend" name="order"/>
			</form>
			<!-- Hidden Form For Order Resend to DOM -->



			<!-- Form For Order change City -->
			<div id="changeCityOrder" class="modal fade">
				<div class="modal-dialog">
					<div class="modal-content">
						<form action="order/changeCity/multiple" method="POST"  enctype="multipart/form-data">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal">&times;</button>
								<h5 class="modal-title">Change Destination City of Multiple Orders</h5>
							</div>
							{{ csrf_field() }}

							<div class="modal-body">
								<div class="form-group">
									<div class="col-lg-4">
										<h6 class="text-semibold">Select Destination City : </h6>

									</div>
									<div class="col-lg-8">
										<input type="hidden" id="selectedOrderChangeCity" name="order">

										<select name="city" class="select-remote-data">
										</select>

									</div>
								</div>
							</div>
							<br>

							<div class="modal-footer">
								<button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
								<button type="submit" class="btn btn-primary">Update Orders</button>
							</div>
						</form>
					</div>
				</div>
			</div>
			<!-- Form For Order change City -->






			<!-- Form For Order change Location -->
			<div id="changeLocationOrder" class="modal fade">
				<div class="modal-dialog">
					<div class="modal-content">
						<form action="order/changeLocation/multiple" method="POST"  enctype="multipart/form-data">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal">&times;</button>
								<h5 class="modal-title">Change Pickup Location of Multiple Orders</h5>
							</div>
							{{ csrf_field() }}

							<div class="modal-body">
								<div class="form-group">
									<div class="col-lg-4">
										<h6 class="text-semibold">Select Pickup Location : </h6>

									</div>
									<div class="col-lg-8">
										<input type="hidden" id="selectedOrderChangeLocation" name="order">

										<select name="location" class="select">
											@foreach ($locations as $item)
												<option value="{{ $item->id }}">{{ $item->location_name }}</option>
											@endforeach
										</select>

									</div>
								</div>
							</div>
							<br>

							<div class="modal-footer">
								<button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
								<button type="submit" class="btn btn-primary">Update Orders</button>
							</div>
						</form>
					</div>
				</div>
			</div>
			<!-- Form For Order change Location -->

		

						<!-- Vertical form modal -->
				<div id="modal_cancel_confirm_with_reason" class="modal fade">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal">&times;</button>
								<h3 class="modal-title">Cancel Order(s)</h3>
							</div>

							
								
								<div class="modal-body">
									
									<h6>Are you sure you want to cancel the order? This is irreversible!</h6>
									
									<br><br>
									<label><b>Select Cancellation Reason</b><b style="color:red"> *</b></label>
			
									<select name="reason" data-placeholder="Select Cancellation Reason" class="select" id="reasons_selected" required>
										@foreach ($reasons as $reason)
											<option value="{{$reason->id}}">{{$reason->value}}</option>
										@endforeach
									</select>

								</div>

								<div class="modal-footer">
									<button type="button" class="btn btn-link" data-dismiss="modal">Don't Cancel</button>
									<button type="button" id="accept_bulk_cancel" class="btn btn-danger">Cancel Order</button>
								</div>
							
						</div>
					</div>
				</div>

			

						<!-- Vertical form modal -->
				<div id="modal_cancel_confirm_without_reason" class="modal fade">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal">&times;</button>
								<h3 class="modal-title">Cancel Order(s)</h3>
							</div>

							
								
								<div class="modal-body">
									
									<h6>Please Create Cancellation Reason first before proceeding further.</h6>
									
								

								</div>

								<div class="modal-footer">
									<button type="button" class="btn btn-link" data-dismiss="modal">Cancel</button>
								</div>
							
						</div>
					</div>
				</div>

		


			

<script>

	window.onload = function() {
		// setTimeout(() => {
		
		document.getElementById('fulfilledTable').style.display = 'none';
		document.getElementById('cancelledTable').style.display = 'none';
		

		document.getElementById('fulfilledTableMobile').style.display = 'none';
		document.getElementById('cancelledTableMobile').style.display = 'none';
			
		// }, 500);
	};

	var orderPlacedSearch = true;

	var allTableShow = false;
	var firstTableShow = false;
	var secondTableShow = false;
	var thirdTableShow = false;

	var allTableMobileShow = false;
	var firstTableMobileShow = false;
	var secondTableMobileShow = false;
	var thirdTableMobileShow = false;

	var allSelected = [];
	var pendingSelected = [];
	var fullfilledSelected = [];
	var cancelledSelected = [];

	var allSelectedMobile = [];
	var pendingSelectedMobile = [];
	var fullfilledSelectedMobile = [];
	var cancelledSelectedMobile = [];

	var pending_mobile_clicks = [];
	var fulfilled_mobile_clicks = [];
	var cancelled_mobile_clicks = [];

	var reasons_count = {{count($reasons)}};

	function myFunction(x) {
            
		if (x.matches) {

			$('#all_filter_btn').prop('hidden',true);

			$('#pending_div_3').css('font-size','30px');
			$('#processing_div_3').css('font-size','30px');
			$('#completed_div_3').css('font-size','30px');
			$('#cancelled_div_3').css('font-size','30px');

			$('#all_table_div').hide();
			$('#pending_table_div').hide();
			$('#fulfilled_table_div').hide();
			$('#cancelled_table_div').hide();
			document.getElementById('allTable').style.display = 'none';
			document.getElementById('pendingTable').style.display = 'block';

			$('#pending_table_mobile_div').show();
			$('#fulfilled_table_mobile_div').show();
			$('#cancelled_table_mobile_div').show();
		} else {
			document.getElementById('pendingTable').style.display = 'none';
			document.getElementById('allTable').style.display = 'block';
			$('#all_filter_btn').prop('hidden',false);

			$('#pending_div_3').css('font-size','68px');
			$('#processing_div_3').css('font-size','68px');
			$('#completed_div_3').css('font-size','68px');
			$('#cancelled_div_3').css('font-size','68px');

			$('#all_table_div').show();
			$('#pending_table_div').show();
			$('#fulfilled_table_div').show();
			$('#cancelled_table_div').show();

			$('#pending_table_mobile_div').hide();
			$('#fulfilled_table_mobile_div').hide();
			$('#cancelled_table_mobile_div').hide();
		}
	
	}
	var x = window.matchMedia("(max-width: 767px)")
	myFunction(x)
	x.addListener(myFunction)

	$.extend( $.fn.dataTable.defaults, {
		dom: '<"datatable-header"B><"datatable-scroll-wrap"rt><"datatable-footer"ip>',
		buttons: {
			buttons: [
				{extend: 'selectAll',className: 'btn btn-info'},
				{extend: 'selectNone',className: 'btn btn-info'},
				{extend: 'pageLength',className: 'btn bg-slate-600'},
				{
					extend: 'collection',
					text: 'Export',
					className: 'btn bg-indigo-400 btn-icon',
					buttons: [
						{extend: 'copyHtml5',text: '<i class="icon-copy3"></i>&nbsp &nbsp Copy ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						},
						{extend: 'csvHtml5',text: '<i class="icon-file-excel"></i>&nbsp &nbsp CSV ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						},
						{extend: 'pdfHtml5',text: '<i class="icon-printer2"></i>&nbsp &nbsp pdf ',className: 'btn btn-default',
							exportOptions: {
								columns: ':visible'
							}
						}
					]
				},
				{extend: 'colvis',columns: ':gt(1)',text: '<i class="icon-grid3"></i> <span class="caret"></span>',className: 'btn bg-indigo-400 btn-icon'}
			]
		},
		select: {
			style: 'os',
			selector: 'td:first-child'
		},
		colReorder: true,
		stateSave: true,
		scrollX: true,
		scrollY: '50vh',
		scrollCollapse: true,
		"deferLoading": true,
		"processing": true,
		"language": {"processing": '<i style="color:green;font-size:50px" class="icon-spinner4 spinner"></i>'}

	});

	Date.prototype.addDays = function(days) {
		var date = new Date(this.valueOf());
		date.setDate(date.getDate() + days);
		return date;
	}


	


    $(document).ready(function() {



		$(".select-remote-data").select2({
			ajax: {
				url: "/seller/cities/search",
				dataType: 'json',
				delay: 250,
				data: function (params) {
					return {
						string: params.term, // search term
					};
				},
				processResults: function (data) {
					return {
						results: $.map(data, function (item) {
							return {
								text: item.name+" | "+item.country_code,
								id: item.name
							}
						})
					};
				},
				cache: true
			},
			minimumInputLength: 1
		});

		$('.daterange-basic').daterangepicker({
			opens: 'center',
			drops: 'up',
			applyClass: 'bg-slate-600',
			cancelClass: 'btn-default',
			locale: {
				format: 'YYYY/MM/DD'
			}
		});
		$('.daterange-basic').val('');	
		
		allTable();
		firstTableMobile();

		
		
	});



	

	$('.daterange-basic').on('apply.daterangepicker', function(ev, picker) {
		orderPlacedSearch = true;
	});
	$('.daterange-basic').on('cancel.daterangepicker', function(ev, picker) {
		orderPlacedSearch = false;
		$(this).val('');
	});


	function allTable() {

		/////////For Pending Table///////////



		// Individual column searching with text inputs
		$('#zeroTable tfoot td').each(function () {
			var title = $(this).text();
			if (!$(this).attr('id') && title) {
				$(this).html('<input type="text" class="form-control input-sm" placeholder="Search '+title+'" />');
			}
		});
		console.log(11);
		var allTable = $('#zeroTable').DataTable( {
			lengthMenu: [
				[ 20, 30, 50, 100, 500, 2000, -1],
				[ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
			],
			retrieve: true,
			"serverSide": true,
			"ajax": "/seller/order/all_main",
			columnDefs: [ {
				orderable: false,
				className: 'select-checkbox',
				targets:   1
			},
			{
				orderable: false,
				searchable: false,
				targets:   6
			}],
			columns: [
				
				{ data: 'marketplace_reference_id',name: 'marketplace_reference_id', render: function(data, type, row) {
						return ('<a target="_blank" href="order/'+row.id+'">'+data+'</a>');
					}  
				},
				{  render: function(data, type, row) {
						return (' ');
					}
				},
				
				{  data: 'status', name: 'status', render: function(data, type, row) {
						if(data == 'Pending' || data == 'Processing'){
							return '<td><span class="label label-info">'+data+'</span></td>'
						} else if(data == 'Completed'){
							return '<td><span class="label label-success">'+data+'</span></td>'
						} else if(data == 'Cancelled'){
							return '<td><span class="label label-danger">'+data+'</span></td>'
						}
					}
				},
				{  data: 'tag', name: 'tag.value', orderable: true, render: function(data, type, row) {
						
						var temp = '';
						data.forEach(element => {
							temp+='<td><span class="label '+element.color+'">'+element.value+'</span></td>';
						})

						return temp;
					}
				},


                                
                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                    {  data: 'customer_name', name: 'customer_name' },
                    { data: 'customer_number',name: 'customer_number', render: function(data, type, row) {
                        try {
                            
                            const regex = /\s/;
                            if (typeof row.customer_number !== 'undefined' && row.customer_number  && !row.customer_number.match(regex)) {

                                    const parsePhoneNumber = libphonenumber.parsePhoneNumber;
                                    const phoneNumber = parsePhoneNumber(row.customer_number, row.country);
                                    return ('<a href="https://web.whatsapp.com/send?phone='+phoneNumber.number+'" target="_blank"<span class="label label-success"><i class="fa fa-whatsapp style="font-size: 14px"></i></span></a> &nbsp;' + data);
                                }

                        } catch (error) {

                        }
                        return ('-');
                                
                    } },
                    {  data: 'shipping_address', name: 'shipping_address', render:function(data, type, row){
                        return (data ? '<div style="width: 250px;overflow: hidden;text-overflow: ellipsis; ">'+data+'</div>' : '-');
                    } },
                    {  data: 'likelihood', name: 'likelihood', orderable:true, render: function(data, type, row) {
                            if(data == '33'){
                                return '<td><span class="label label-danger">Low</span></td>';
                            } else if(data == '66'){
                                return '<td><span class="label bg-orange">Medium</span></td>';
                            } else if(data == '99'){
                                return '<td><span class="label label-success">High</span></td>';
                            } else if(data == '0'){
                                return '<td><span class="label label-danger">Invalid</span></td>';
                            } else{
                                return '';
                            }
                        } 
                    },
                    {  data: 'customer_email', name: 'customer_email' },

                @else

                    {  data: 'likelihood', name: 'likelihood', orderable:true, render: function(data, type, row) {
                            if(data == '33'){
                                return '<td><span class="label label-danger">Low</span></td>';
                            } else if(data == '66'){
                                return '<td><span class="label bg-orange">Medium</span></td>';
                            } else if(data == '99'){
                                return '<td><span class="label label-success">High</span></td>';
                            } else if(data == '0'){
                                return '<td><span class="label label-danger">Invalid</span></td>';
                            } else{
                                return '';
                            }
                        } 
                    },

                @endif

				
				{  data: 'created_date', name: 'created_date', render:function(data, type, row){
					return (data)?moment(data.date).format('YYYY-MM-DD HH:mm:ss'):'-';
				} },
				{  data: 'location_name', name: 'location_name' ,render: function(data, type, row) {
						if(data == 'Undefined' || data == 'undefined'){
							return 'No Location assigned';
						} else{
							return data;
						}
					}},
				{  data: 'destination_city', name: 'destination_city' },
				
				{  data: 'city_exists', name: 'city_exists.name', render: function(data, type, row) {
						return (data ? '<td><span class="label label-success">Yes</span></td>' : '<td><span class="label label-danger">No</span></td>');
					}
				},

				{  data: 'seller_blacklist_customer', name: 'seller_blacklist_customer.contact_number', render: function(data, type, row) {
						return (data ? '<td><span class="label label-success">Yes</span></td>' : '<td><span class="label label-danger">No</span></td>');
					}
				},

				{  data: 'customer_stats.return_ratio', name: 'customer_stats.return_ratio', searchable:false, orderable: false, render: function(data, type, row) {
						return (data && data != 0 ? (parseFloat(data).toFixed(2))+' %' : '-');
					}
				},
				
				{  data: 'country', name: 'country'},
				
				
				{  data: 'cod_payment', name: 'cod_payment', render: function(data, type, row) {
						return (data == 1 ? '<td><span class="label label-success">Yes</span></td>' : '<td><span class="label label-danger">No</span></td>');
					}
				},
				{  data: 'display_name', name: 'seller_payment_methods.id' },
				{  data: 'grand_total', name: 'grand_total', orderable:true },
				{ data: 'order_cancellation_reason', name: 'order_cancellation_reason' , orderable: false, searchable: false , render: function(data, type, row) {
						return (data != null ? '<td><span class="label '+row.order_cancellation_reason.reason.color+'">'+row.order_cancellation_reason.reason.value+'</span></td>': '')
				} },
				

			],
			"order": [[9, 'desc']],
			"rowCallback": function( row, data ) {
				if ( $.inArray(data.DT_RowId, allSelected) !== -1 ) {
					allTable.row(row).select();
				}
			}
		});
		console.log(22);
		allTable.columns().search( '' ).draw();


		allTable.columns().every( function () {
			var that = this;
			$('input', this.footer()).on('keyup change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							if (orderPlacedSearch) {
								var start = new Date (((this.value).split('-'))[0]);
								var end = new Date (((this.value).split('-'))[1]);
								var dateArray = new Array();
								while (start <= end) {
									start = start.addDays(1);
									dateArray.push((new Date (start)).toISOString().slice(0,10));
								}
								var range = dateArray.join('|');
								that.search(range,true,false).draw();

							} else {
								that.search('',true,false).draw();
							}
							
						}, 500);

					} else {
						if((this.value).toLowerCase() == 'no location assigned'){
							that.search('undefined',true,false).draw();
						} else{

							// that.search(((this.value).trim()).replace(/\|$/, ''),true,false).draw();
							that.search(((this.value).trim()).replace(/ /g, '|'),true,false).draw();
						}
						
					}
				}
			});

			$('.select', this.footer()).on('change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							that.search(this.value,true,false).draw();
						}, 500);
					}
				}
			});
		});

		$('#zeroTable tbody').on('click', 'tr', function() {
			if($(this).hasClass('selected'))
			{
				allTable.row($(this)).deselect();
			} else {
				allTable.row($(this)).select();
			}
		});


		////////////////// Function Run on select ///////////
		allTable.on( 'select', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = allTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], allSelected);
					if (idIndex === -1) {
						allSelected.push( rows[index] );
					}
				}

			}
			$('.zeroTableAction').html('( '+allSelected.length+' ) Action <span class="caret"></span>');
		});


		////////////////// Function Run on deselect ///////////
		allTable.on( 'deselect', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = allTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], allSelected);
					allSelected.splice( idIndex, 1 );
				}

			}
			if (allSelected.length == 0) {
				$('.zeroTableAction').html(' Action <span class="caret"></span>');
			} else {
				$('.zeroTableAction').html('( '+allSelected.length+' ) Action <span class="caret"></span>');
			}
		});

		var addTagLI = '<li onclick=addTag("all")><a ><i class="icon-price-tag2"></i>Add Tag</a></li>';
		var removeTagLI = '<li onclick=removeTag("all")><a class="removeTag"><i class="icon-price-tag2"></i>Remove Tag</a></li>';
		var changeCityLI = '<li onclick=changeCity("all")><a class="changeCity"><i class="icon-location3"></i>Change Destination City</a></li>';
		var changeLocationLI = '<li onclick=changeLocation("all")><a class="changeCity"><i class="icon-location3"></i>Change Pickup Location</a></li>';
		var smsTriggerLI = '<li onclick=smsTrigger("all")><a class="smsTrigger"><i class="icon-envelop"></i>SMS</a></li>';
		var roboCallLI = '<li onclick=roboCall("all")><a class="roboCall"><i class="icon-phone-outgoing"></i>Robocall</a></li>';
		var exportOrderLI = '<li onclick=exportOrder("all")><a class="exportOrder"><i class="icon-share"></i>Export Order Details</a></li>';
		var invoiceLI = '<li onclick=invoice("all")><a class="invoice"><i class="icon-printer"></i>Print Invoice</a></li>';
		var bulkCancellation = '<li onclick=bulkCancelAlert("all")><a class="bulkCancellation"><i class="icon-blocked"></i>Bulk Cancellation</a></li>';
		var resentToDOMLI = '<li onclick=reSendToDOM("all")><a class="resenddom"><i class="icon-reset"></i>Re-send to DOM</a></li>';

		@if(session()->has('permission'))
			@if(session('permission')->order_tagging)
				var allLI = addTagLI+removeTagLI+exportOrderLI+invoiceLI+changeCityLI+changeLocationLI;
			@else
				var allLI = exportOrderLI+invoiceLI+changeCityLI+changeLocationLI;
			@endif
		@else
			var allLI = addTagLI+removeTagLI+exportOrderLI+invoiceLI+changeCityLI+changeLocationLI+bulkCancellation;
		@endif

		@if($roboCall)
				allLI += smsTriggerLI;
				allLI += roboCallLI;
		@endif

		@if($omniLocationAssignment)
			allLI += resentToDOMLI;
		@endif

		$( "#zeroTable_wrapper .datatable-header" ).append( '<div class="dataTables_filter"><button type="button" class="btn btn-danger zeroTableAction dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button><ul class="dropdown-menu">'+allLI+'</ul></div>' );

	}

	function firstTable() {

		/////////For Pending Table///////////

		firstTableShow = true;

		// Individual column searching with text inputs
		$('#firstTable tfoot td').each(function () {
			var title = $(this).text();
			if (!$(this).attr('id') && title) {
				$(this).html('<input type="text" class="form-control input-sm" placeholder="Search '+title+'" />');
			}
		});

		var firstTable = $('#firstTable').DataTable( {
			lengthMenu: [
				[ 20,30,50,100, 500, 2000, -1],
				[ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
			],
			retrieve: true,
			"serverSide": true,
			"ajax": "/seller/order/all_pending",
			columnDefs: [ {
				orderable: false,
				className: 'select-checkbox',
				targets:   1
			},
			{
				orderable: false,
				searchable: false,
				targets:   6
			}],
			columns: [
				
				{ data: 'marketplace_reference_id',name: 'marketplace_reference_id', render: function(data, type, row) {
						return ('<a target="_blank" href="order/'+row.id+'">'+data+'</a>');
					}  
				},
				{  render: function(data, type, row) {
						return (' ');
					}
				},
				
				{  data: 'status', name: 'status', render: function(data, type, row) {
						return '<td><span class="label label-info">'+data+'</span></td>'
					}
				},
				{  data: 'tag', name: 'tag.value', orderable: true, render: function(data, type, row) {
						
						var temp = '';
						data.forEach(element => {
							temp+='<td><span class="label '+element.color+'">'+element.value+'</span></td>';
						})

						return temp;
					}
				},

                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                    {  data: 'customer_name', name: 'customer_name' },
                    { data: 'customer_number',name: 'customer_number', render: function(data, type, row) {
                        try {
                            
                            const regex = /\s/;
                            if (typeof row.customer_number !== 'undefined' && row.customer_number  && !row.customer_number.match(regex)) {

                                    const parsePhoneNumber = libphonenumber.parsePhoneNumber;
                                    const phoneNumber = parsePhoneNumber(row.customer_number, row.country);
                                    return ('<a href="https://web.whatsapp.com/send?phone='+phoneNumber.number+'" target="_blank"<span class="label label-success"><i class="fa fa-whatsapp style="font-size: 14px"></i></span></a> &nbsp;' + data);
                                }

                        } catch (error) {

                        }
                        return ('-');
                    } },
                    {  data: 'shipping_address', name: 'shipping_address', render:function(data, type, row){
                        return (data ? '<div style="width: 250px;overflow: hidden;text-overflow: ellipsis; ">'+data+'</div>' : '-');
                    } },
                    {  data: 'likelihood', name: 'likelihood', orderable:true, render: function(data, type, row) {
                            if(data == '33'){
                                return '<td><span class="label label-danger">Low</span></td>';
                            } else if(data == '66'){
                                return '<td><span class="label bg-orange">Medium</span></td>';
                            } else if(data == '99'){
                                return '<td><span class="label label-success">High</span></td>';
                            } else if(data == '0'){
                                return '<td><span class="label label-danger">Invalid</span></td>';
                            } else{
                                return '';
                            }
                        } 
                    },
                    {  data: 'customer_email', name: 'customer_email' },

                @else

                    {  data: 'likelihood', name: 'likelihood', orderable:true, render: function(data, type, row) {
                            if(data == '33'){
                                return '<td><span class="label label-danger">Low</span></td>';
                            } else if(data == '66'){
                                return '<td><span class="label bg-orange">Medium</span></td>';
                            } else if(data == '99'){
                                return '<td><span class="label label-success">High</span></td>';
                            } else if(data == '0'){
                                return '<td><span class="label label-danger">Invalid</span></td>';
                            } else{
                                return '';
                            }
                        } 
                    },

                @endif
				
				{  data: 'created_date', name: 'created_date', render:function(data, type, row){
					return (data)?moment(data.date).format('YYYY-MM-DD HH:mm:ss'):'-';
				} },
				{  data: 'location_name', name: 'location_name' ,render: function(data, type, row) {
						if(data == 'Undefined' || data == 'undefined'){
							return 'No Location assigned';
						} else{
							return data;
						}
					}},
				{  data: 'destination_city', name: 'destination_city' },
				
				{  data: 'city_exists', name: 'city_exists.name', render: function(data, type, row) {
						return (data ? '<td><span class="label label-success">Yes</span></td>' : '<td><span class="label label-danger">No</span></td>');
					}
				},
				
				{  data: 'country', name: 'country'},
				
				
				{  data: 'cod_payment', name: 'cod_payment', render: function(data, type, row) {
						return (data == 1 ? '<td><span class="label label-success">Yes</span></td>' : '<td><span class="label label-danger">No</span></td>');
					}
				},
				{  data: 'display_name', name: 'seller_payment_methods.id' },
				{  data: 'grand_total', name: 'grand_total', orderable:true },
				
				
			],
			"order": [[9, 'desc']],
			"rowCallback": function( row, data ) {
				if ( $.inArray(data.DT_RowId, pendingSelected) !== -1 ) {
					firstTable.row(row).select();
				}
			}
		});
		firstTable.columns().search( '' ).draw();


		firstTable.columns().every( function () {
			var that = this;
			$('input', this.footer()).on('keyup change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							if (orderPlacedSearch) {
								var start = new Date (((this.value).split('-'))[0]);
								var end = new Date (((this.value).split('-'))[1]);
								var dateArray = new Array();
								while (start <= end) {
									start = start.addDays(1);
									dateArray.push((new Date (start)).toISOString().slice(0,10));
								}
								var range = dateArray.join('|');
								that.search(range,true,false).draw();

							} else {
								that.search('',true,false).draw();
							}
							
						}, 500);

					} else {
						if((this.value).toLowerCase() == 'no location assigned'){
							that.search('undefined',true,false).draw();
						} else{

							// that.search(((this.value).trim()).replace(/\|$/, ''),true,false).draw();
							that.search(((this.value).trim()).replace(/ /g, '|'),true,false).draw();
						}
					}
				}
			});

			$('.select', this.footer()).on('change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							that.search(this.value,true,false).draw();
						}, 500);
					}
				}
			});
		});

		$('#firstTable tbody').on('click', 'tr', function() {
			if($(this).hasClass('selected'))
			{
				firstTable.row($(this)).deselect();
			} else {
				firstTable.row($(this)).select();
			}
		});


		////////////////// Function Run on select ///////////
		firstTable.on( 'select', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = firstTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], pendingSelected);
					if (idIndex === -1) {
						pendingSelected.push( rows[index] );
					}
				}

			}
			$('.firstTableAction').html('( '+pendingSelected.length+' ) Action <span class="caret"></span>');
		});


		////////////////// Function Run on deselect ///////////
		firstTable.on( 'deselect', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = firstTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], pendingSelected);
					pendingSelected.splice( idIndex, 1 );
				}

			}
			if (pendingSelected.length == 0) {
				$('.firstTableAction').html(' Action <span class="caret"></span>');
			} else {
				$('.firstTableAction').html('( '+pendingSelected.length+' ) Action <span class="caret"></span>');
			}
		});
		
		var addTagLI = '<li onclick=addTag("first")><a ><i class="icon-price-tag2"></i>Add Tag</a></li>';
		var removeTagLI = '<li onclick=removeTag("first")><a class="removeTag"><i class="icon-price-tag2"></i>Remove Tag</a></li>';
		var changeCityLI = '<li onclick=changeCity("first")><a class="changeCity"><i class="icon-location3"></i>Change Destination City</a></li>';
		var changeLocationLI = '<li onclick=changeLocation("first")><a class="changeCity"><i class="icon-location3"></i>Change Pickup Location</a></li>';
		var smsTriggerLI = '<li onclick=smsTrigger("first")><a class="smsTrigger"><i class="icon-envelop"></i>SMS</a></li>';
		var roboCallLI = '<li onclick=roboCall("first")><a class="roboCall"><i class="icon-phone-outgoing"></i>Robocall</a></li>';
		var exportOrderLI = '<li onclick=exportOrder("first")><a class="exportOrder"><i class="icon-share"></i>Export Order Details</a></li>';
		var invoiceLI = '<li onclick=invoice("first")><a class="invoice"><i class="icon-printer"></i>Print Invoice</a></li>';
		var bulkCancellation = '<li onclick=bulkCancelAlert("first")><a class="bulkCancellation"><i class="icon-blocked"></i>Bulk Cancellation</a></li>';
		var resentToDOMLI = '<li onclick=reSendToDOM("first")><a class="resenddom"><i class="icon-reset"></i>Re-send to DOM</a></li>';

		@if(session()->has('permission'))
			@if(session('permission')->order_tagging)
				var allLI = addTagLI+removeTagLI+exportOrderLI+invoiceLI+changeCityLI+changeLocationLI;
			@else
				var allLI = exportOrderLI+invoiceLI+changeCityLI+changeLocationLI;
			@endif
		@else
			var allLI = addTagLI+removeTagLI+exportOrderLI+invoiceLI+changeCityLI+changeLocationLI+bulkCancellation;
		@endif

		@if($roboCall)
				allLI += smsTriggerLI;
				allLI += roboCallLI;
		@endif

		@if($omniLocationAssignment)
			allLI += resentToDOMLI;
		@endif


		$( "#firstTable_wrapper .datatable-header" ).append( '<div class="dataTables_filter"><button type="button" class="btn btn-danger firstTableAction dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button><ul class="dropdown-menu">'+allLI+'</ul></div>' );
		
	}

	function firstTableMobile() {

		/////////For Pending Table///////////

		var search_arr = [];

		// Individual column searching with text inputs
		$('.daterange-basic').daterangepicker({
			opens: 'center',
			drops: 'up',
			applyClass: 'bg-slate-600',
			cancelClass: 'btn-default',
			locale: {
				format: 'YYYY/MM/DD'
			}
		});
		$('.daterange-basic').val('');	

		var firstTableMobile = $('#firstTableMobile').DataTable( {
			lengthMenu: [
				[ 20,30,50,100, 500, 2000, -1],
				[ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
			],
			buttons: [
				{extend: 'selectAll',className: 'btn btn-info'},
				{extend: 'selectNone',className: 'btn btn-info'},
				{extend: 'pageLength',className: 'btn bg-slate-600'},
				{
					extend: 'collection',
					text: '<i class="icon-filter4"></i> <span class="caret"></span>',
					className: 'btn bg-danger',
					buttons: [
						{extend: 'copyHtml5',text: '<i class="icon-filter3"></i>&nbsp &nbsp Filter ',className: 'btn btn-default',
							action: function ( e, dt, node, config ) {
								if($('#f2').val() != ''){
									this.column(2).search((($('#f2').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('2')){
										search_arr.push('2');
									}
								} else if(search_arr.includes('2')){
									var ind = search_arr.indexOf('2');
									if(ind > -1){
										this.column(2).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f3').val() != ''){
									this.column(3).search((($('#f3').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('3')){
										search_arr.push('3');
									}
								}else{
									var ind = search_arr.indexOf('3');
									if(ind > -1){
										this.column(3).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f4').val() != ''){
									var start = new Date ((($('#f4').val()).split('-'))[0]);
									var end = new Date ((($('#f4').val()).split('-'))[1]);
									var dateArray = new Array();
									while (start <= end) {
										start = start.addDays(1);
										dateArray.push((new Date (start)).toISOString().slice(0,10));
									}
									var range = dateArray.join('|');
									this.column(4).search(range,true,false).draw();
									if(!search_arr.includes('4')){
										search_arr.push('4');
									}
								}
								else{
									var ind = search_arr.indexOf('4');
									if(ind > -1){
										this.column(4).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f5').val() != ''){
									this.column(5).search((($('#f5').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('5')){
										search_arr.push('5');
									}
								}
								else{
									var ind = search_arr.indexOf('5');
									if(ind > -1){
										this.column(5).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f6').val() != ''){
									if(($('#f6').val()).toLowerCase() == 'no location assigned'){
										this.column(6).search('undefined',true,false).draw();
									} else{
										this.column(6).search((($('#f6').val()).trim()).replace(/ /g, '|'),true,false).draw();
									}
									
									if(!search_arr.includes('6')){
										search_arr.push('6');
									}
								}
								else{
									var ind = search_arr.indexOf('6');
									if(ind > -1){
										this.column(6).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f7').val() != ''){
									this.column(7).search((($('#f7').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('7')){
										search_arr.push('7');
									}
								}else{
									var ind = search_arr.indexOf('7');
									if(ind > -1){
										this.column(7).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f8').val() != ''){
									this.column(8).search((($('#f8').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('8')){
										search_arr.push('8');
									}
								}else{
									var ind = search_arr.indexOf('8');
									if(ind > -1){
										this.column(8).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f9').val() != ''){
									this.column(9).search((($('#f9').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('9')){
										search_arr.push('9');
									}
								}else{
									var ind = search_arr.indexOf('9');
									if(ind > -1){
										this.column(9).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f10').val() != ''){
									this.column(10).search((($('#f10').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('10')){
										search_arr.push('10');
									}
								}else{
									var ind = search_arr.indexOf('10');
									if(ind > -1){
										this.column(10).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f11').val() != ''){
									this.column(11).search((($('#f11').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('11')){
										search_arr.push('11');
									}
								}else{
									var ind = search_arr.indexOf('11');
									if(ind > -1){
										this.column(11).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f12').val() != ''){
									this.column(12).search((($('#f12').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('12')){
										search_arr.push('12');
									}
								}else{
									var ind = search_arr.indexOf('12');
									if(ind > -1){
										this.column(12).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f13').val() != ''){
									this.column(13).search($('#f13').val(),true,false).draw();
									if(!search_arr.includes('13')){
										search_arr.push('13');
									}
								}else{
									var ind = search_arr.indexOf('13');
									if(ind > -1){
										this.column(13).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

							
								
							}
						},
						{text: '<input type="text" id="f2" placeholder="Reference Order" class="form-control">',className: ''},
						{text: '<input type="text" id="f3" placeholder="Destination City" class="form-control">',className: ''},
						{text: '<input type="text" id="f4" placeholder="Order Placed" class="form-control daterange-basic">'},
						{text: '<input type="text" id="f5"placeholder="Country" class="form-control">',className: ''},

						{text: '<input type="text" id="f6" placeholder="Pickup Location" class="form-control">',className: ''},
						{text: '<input type="text" id="f7" placeholder="Status" class="form-control">',className: ''},
						{text: '<select id="f13" class="form-control"> <option value="">All (COD Status)</option><option value="1">COD</option><option value="0">PAID</option> </select>',className: ''},
						{text: '<input type="text" id="f8" placeholder="Grand Total" class="form-control">',className: ''},

                        @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                            {text: '<input type="text" id="f9" placeholder="Customer Name" class="form-control">',className: ''},

                            {text: '<input type="text" id="f10" placeholder="Customer Email" class="form-control">',className: ''},
                            {text: '<input type="text" id="f11" placeholder="Customer Number" class="form-control">',className: ''},

                        @endif
                        {text: '<input type="text" id="f12" placeholder="Tag" class="form-control">',className: ''},

						
					]
				},
			],
			scrollX: false,
			"serverSide": true,
			"ajax": "/seller/order/all_pending",
			columnDefs: [ {
				orderable: false,
				className: 'select-checkbox',
				targets:   0,
			} ,
			{ width: '100%', targets: 1 },
			{
				targets: [2,3,4,5,6,7,8,9,10,11,12,13],
				searchable: true,
				visible: false,
				orderable: false
			}
			],
			"pagingType": "numbers",
			columns: [
				
				{ render: function(data, type, row) {
						return (' '); 
					}
				},
				
				{ data: 'id',name: 'id', render: function(data, type, row) {
					var temp = ''
					var pick_loc = '';
					row.tag.forEach(element => {
							temp+='<span class="label '+element.color+'">'+element.value+'</span>';
						});

						
						if(row.location_name == 'Undefined' || row.location_name == 'undefined'){
							pick_loc = 'No Location assigned';
						} else{
							pick_loc =  row.location_name;
						}
					

                        @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                            return ('<div style=""><div style="float:left;text-align:left;width:60%"><a target="_blank" style="font-size:18px;" href="order/'+data+'"># '+row.marketplace_reference_id+'</a> <br> <span class="label label-info">'+row.status+'</span> <br><br><i class="icon-calendar3"></i> '+(row.created_date?moment(row.created_date.date).format('YYYY-MM-DD HH:mm:ss'):'-')+'<br><i class="icon-person"></i> '+row.customer_name+'<br><i class="icon-mobile"></i> '+row.customer_number+'<br><i class="icon-location3"></i> '+row.destination_city+' - '+row.country+'<br><i class="icon-store"></i> '+pick_loc+'<br><br>'+temp+'</div><div style="float:right;text-align:center;width:40%;"><span style="font-size:16px;font-weight:bold">'+row.currency+' '+row.grand_total+'</span><br>'+(row.cod_payment == true ? '<span class="label label-success">COD</span>' : '<span class="label label-danger">PAID</span>')+'</div></div>');
                        @else
                            return ('<div style=""><div style="float:left;text-align:left;width:60%"><a target="_blank" style="font-size:18px;" href="order/'+data+'"># '+row.marketplace_reference_id+'</a> <br> <span class="label label-info">'+row.status+'</span> <br><br><i class="icon-calendar3"></i> '+(row.created_date?moment(row.created_date.date).format('YYYY-MM-DD HH:mm:ss'):'-')+'<br><i class="icon-location3"></i> '+row.destination_city+' - '+row.country+'<br><i class="icon-store"></i> '+pick_loc+'<br><br>'+temp+'</div><div style="float:right;text-align:center;width:40%;"><span style="font-size:16px;font-weight:bold">'+row.currency+' '+row.grand_total+'</span><br>'+(row.cod_payment == true ? '<span class="label label-success">COD</span>' : '<span class="label label-danger">PAID</span>')+'</div></div>');
                        @endif
					}  
				},


				{ data: 'marketplace_reference_id',name: 'marketplace_reference_id'},
				
				{  data: 'destination_city', name: 'destination_city' },
				
				{  data: 'created_date', name: 'created_date'},
				{  data: 'country', name: 'country'},
				{  data: 'location_name', name: 'location_name' },
				{  data: 'status', name: 'status'},
				{  data: 'grand_total', name: 'grand_total' },
				{  data: 'customer_name', name: 'customer_name' },
				{  data: 'customer_email', name: 'customer_email' },
				{  data: 'customer_number', name: 'customer_number' },
				{  data: 'tag', name: 'tag'},
				{  data: 'cod_payment', name: 'cod_payment'},
				
			],
			"order": [[1, 'desc']],
			"rowCallback": function( row, data ) {
				if ( $.inArray(data.DT_RowId, pendingSelected) !== -1 ) {
					firstTableMobile.row(row).select();
				}
			}
		});
		firstTableMobile.columns().search( '' ).draw();


		

		$('#firstTableMobile tbody').on('click', 'tr', function() {
			if($(this).hasClass('selected'))
			{
				firstTableMobile.row($(this)).deselect();
			} else {
				firstTableMobile.row($(this)).select();
			}
		});


		////////////////// Function Run on select ///////////
		firstTableMobile.on( 'select', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				var rows = firstTableMobile.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], pendingSelected);
					if (idIndex === -1) {
						pendingSelected.push( rows[index] );
					}

				}
			}
			$('.firstTableMobileAction').html('( '+pendingSelected.length+' ) Action <span class="caret"></span>');
		});


		////////////////// Function Run on deselect ///////////
		firstTableMobile.on( 'deselect', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = firstTableMobile.rows( indexes ).data().pluck( 'id' );
				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], pendingSelected);
					pendingSelected.splice( idIndex, 1 );
				}

			}
			if (pendingSelected.length == 0) {
				$('.firstTableMobileAction').html(' Action <span class="caret"></span>');
			} else {
				$('.firstTableMobileAction').html('( '+pendingSelected.length+' ) Action <span class="caret"></span>');
			}
		});

		var addTagLI = '<li onclick=addTag("first")><a >Add Tag</a></li>';
		var removeTagLI = '<li onclick=removeTag("first")><a class="removeTag">Remove Tag</a></li>';
		var changeCityLI = '<li onclick=changeCity("first")><a class="changeCity">Change Destination City</a></li>';
		var changeLocationLI = '<li onclick=changeLocation("first")><a class="changeCity">Change Pickup Location</a></li>';
		var smsTriggerLI = '<li onclick=smsTrigger("first")><a class="smsTrigger">smsTrigger</a></li>';
		var roboCallLI = '<li onclick=roboCall("first")><a class="roboCall">Robocall</a></li>';
		var exportOrderLI = '<li onclick=exportOrder("first")><a class="exportOrder"> Export Order Details</a></li>';
		var invoiceLI = '<li onclick=invoice("first")><a class="invoice"><i class="icon-printer"></i>Print Invoice</a></li>';
		var bulkCancellation = '<li onclick=bulkCancelAlert("first")><a class="bulkCancellation">Bulk Cancellation</a></li>';

		@if(session()->has('permission'))
			@if(session('permission')->order_tagging)
				var allLI = addTagLI+removeTagLI+exportOrderLI+invoiceLI+changeCityLI+changeLocationLI;
			@else
				var allLI = exportOrderLI+invoiceLI+changeCityLI+changeLocationLI;
			@endif
		@else
			var allLI = addTagLI+removeTagLI+exportOrderLI+invoiceLI+changeCityLI+changeLocationLI+bulkCancellation;
		@endif

		@if($roboCall)
				allLI += smsTriggerLI;
				allLI += roboCallLI;
		@endif

		@if($omniLocationAssignment)
			allLI += resentToDOMLI;
		@endif

		$( "#firstTableMobile_wrapper .datatable-header" ).append( '<div class="btn-group"><div class="dataTables_filter"><button type="button" class="btn btn-danger firstTableAction dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button><ul class="dropdown-menu">'+allLI+'</ul></div></div>' );

	}

	function secondTable() {

		secondTableShow = true;

		// Individual column searching with text inputs
		$('#secondTable tfoot td').each(function () {
			var title = $(this).text();
			if (!$(this).attr('id') && title) {
				$(this).html('<input type="text" class="form-control input-sm" placeholder="Search '+title+'" />');
			}
		});

		/////////For Fulfilled Table///////////
		var secondTable = $('#secondTable').DataTable( {
			lengthMenu: [
				[ 20,30,50,100, 500, 2000, -1],
				[ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
			],
			"serverSide": true,
			"ajax": "/seller/order/all_fulfilled",
			columnDefs: [ {
				orderable: false,
				className: 'select-checkbox',
				targets:   1
			} ,
			{
				orderable: false,
				searchable: false,
				targets:   6
			}],
			columns: [
				{ data: 'marketplace_reference_id',name: 'marketplace_reference_id', render: function(data, type, row) {
						return ('<a target="_blank" href="order/'+row.id+'">'+data+'</a>');
					}  
				},
				{  render: function(data, type, row) {
						return (' ');
					}
				},
				{  data: 'status', name: 'status', render: function(data, type, row) {
						return '<td><span class="label label-success">'+data+'</span></td>'
					}
				},
				{  data: 'tag', name: 'tag.value', orderable: true, render: function(data, type, row) {
						
						var temp = '';
						data.forEach(element => {
							temp+='<td><span class="label '+element.color+'">'+element.value+'</span></td>';
						})

						return temp;
					}
				},

                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                    {  data: 'customer_name', name: 'customer_name' },
                    {  data: 'customer_number', name: 'customer_number' },
                    {  data: 'shipping_address', name: 'shipping_address', render:function(data, type, row){
                        return (data ? '<div style="width: 250px;overflow: hidden;text-overflow: ellipsis; ">'+data+'</div>' : '-');
                    } },
                    
                    {  data: 'customer_email', name: 'customer_email' },

                @endif
				
				
				{  data: 'created_date', name: 'created_date', render:function(data, type, row){
					return (data)?moment(data.date).format('YYYY-MM-DD HH:mm:ss'):'-';
				} },
				{  data: 'location_name', name: 'location_name' ,render: function(data, type, row) {
						if(data == 'Undefined' || data == 'undefined'){
							return 'No Location assigned';
						} else{
							return data;
						}
					}},
				{  data: 'destination_city', name: 'destination_city' },
				{  data: 'country', name: 'country'},
				
				
				{  data: 'cod_payment', name: 'cod_payment', render: function(data, type, row) {
						return (data == 1 ? '<td><span class="label label-success">Yes</span></td>' : '<td><span class="label label-danger">No</span></td>');
					}
				},
				{  data: 'display_name', name: 'seller_payment_methods.id' },
				{  data: 'grand_total', name: 'grand_total' },
				
			],
			order: [[8, 'desc']],
			"rowCallback": function( row, data ) {
				if ( $.inArray(data.DT_RowId, fullfilledSelected) !== -1 ) {
					secondTable.row(row).select();
				}
			}
		});
		secondTable.columns().search( '' ).draw();



		secondTable.columns().every( function () {
			var that = this;
			$('input', this.footer()).on('keyup change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							if (orderPlacedSearch) {
								var start = new Date (((this.value).split('-'))[0]);
								var end = new Date (((this.value).split('-'))[1]);
								var dateArray = new Array();
								while (start <= end) {
									start = start.addDays(1);
									dateArray.push((new Date (start)).toISOString().slice(0,10));
								}
								var range = dateArray.join('|');
								that.search(range,true,false).draw();

							} else {
								that.search('',true,false).draw();
							}
							
						}, 500);

					} else {
						if((this.value).toLowerCase() == 'no location assigned'){
							that.search('undefined',true,false).draw();
						} else{

							// that.search(((this.value).trim()).replace(/\|$/, ''),true,false).draw();
							that.search(((this.value).trim()).replace(/ /g, '|'),true,false).draw();
						}
					}
				}
			});

			$('.select', this.footer()).on('change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							that.search(this.value,true,false).draw();
						}, 500);
					}
				}
			});
		});


		$('#secondTable tbody').on('click', 'tr', function() {
			if($(this).hasClass('selected'))
			{
				secondTable.row($(this)).deselect();
			} else {
				secondTable.row($(this)).select();
			}
		});


		////////////////// Function Run on select ///////////
		secondTable.on( 'select', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = secondTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], fullfilledSelected);
					if (idIndex === -1) {
						fullfilledSelected.push( rows[index] );
					}
				}

			}
			$('.secondTableAction').html('( '+fullfilledSelected.length+' ) Action <span class="caret"></span>');
		});


		////////////////// Function Run on deselect ///////////
		secondTable.on( 'deselect', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = secondTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], fullfilledSelected);
					fullfilledSelected.splice( idIndex, 1 );
				}

			}
			if (fullfilledSelected.length == 0) {
				$('.secondTableAction').html(' Action <span class="caret"></span>');
			} else {
				$('.secondTableAction').html('( '+fullfilledSelected.length+' ) Action <span class="caret"></span>');
			}
		});
		

		var addTagLI = '<li onclick=addTag("second")><a >Add Tag</a></li>';
		var removeTagLI = '<li onclick=removeTag("second")><a class="removeTag">Remove Tag</a></li>';
		var exportOrderLI = '<li onclick=exportOrder("second")><a class="exportOrder"> Export Order Details</a></li>';
		$( "#secondTable_wrapper .datatable-header" ).append( '<div class="dataTables_filter"><button type="button" class="btn btn-danger secondTableAction dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button><ul class="dropdown-menu">'+addTagLI+removeTagLI+exportOrderLI+'</ul></div>' );
	}

	function secondTableMobile() {

		secondTableMobileShow = true;
		var search_arr = [];
		/////////For Fulfilled Table///////////
		var secondTableMobile = $('#fulfilledTableMobile').DataTable( {
			lengthMenu: [
				[ 20,30,50,100, 500, 2000, -1],
				[ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
			],
			buttons: [
				{extend: 'selectAll',className: 'btn btn-info'},
				{extend: 'selectNone',className: 'btn btn-info'},
				{extend: 'pageLength',className: 'btn bg-slate-600'},
				{
					extend: 'collection',
					text: '<i class="icon-filter4"></i> <span class="caret"></span>',
					className: 'btn bg-danger',
					buttons: [
						{extend: 'copyHtml5',text: '<i class="icon-filter3"></i>&nbsp &nbsp Filter ',className: 'btn btn-default',
							action: function ( e, dt, node, config ) {
								if($('#f2').val() != ''){
									this.column(2).search((($('#f2').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('2')){
										search_arr.push('2');
									}
								} else if(search_arr.includes('2')){
									var ind = search_arr.indexOf('2');
									if(ind > -1){
										this.column(2).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f3').val() != ''){
									this.column(3).search((($('#f3').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('3')){
										search_arr.push('3');
									}
								}else{
									var ind = search_arr.indexOf('3');
									if(ind > -1){
										this.column(3).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f4').val() != ''){
									var start = new Date ((($('#f4').val()).split('-'))[0]);
									var end = new Date ((($('#f4').val()).split('-'))[1]);
									var dateArray = new Array();
									while (start <= end) {
										start = start.addDays(1);
										dateArray.push((new Date (start)).toISOString().slice(0,10));
									}
									var range = dateArray.join('|');
									this.column(4).search(range,true,false).draw();
									if(!search_arr.includes('4')){
										search_arr.push('4');
									}
								}
								else{
									var ind = search_arr.indexOf('4');
									if(ind > -1){
										this.column(4).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f5').val() != ''){
									this.column(5).search((($('#f5').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('5')){
										search_arr.push('5');
									}
								}
								else{
									var ind = search_arr.indexOf('5');
									if(ind > -1){
										this.column(5).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f6').val() != ''){
									if(($('#f6').val()).toLowerCase() == 'no location assigned'){
										this.column(6).search('undefined',true,false).draw();
									} else{
										this.column(6).search((($('#f6').val()).trim()).replace(/ /g, '|'),true,false).draw();
									}
									if(!search_arr.includes('6')){
										search_arr.push('6');
									}
								}
								else{
									var ind = search_arr.indexOf('6');
									if(ind > -1){
										this.column(6).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f7').val() != ''){
									this.column(7).search((($('#f7').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('7')){
										search_arr.push('7');
									}
								}else{
									var ind = search_arr.indexOf('7');
									if(ind > -1){
										this.column(7).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f8').val() != ''){
									this.column(8).search((($('#f8').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('8')){
										search_arr.push('8');
									}
								}else{
									var ind = search_arr.indexOf('8');
									if(ind > -1){
										this.column(8).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f9').val() != ''){
									this.column(9).search((($('#f9').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('9')){
										search_arr.push('9');
									}
								}else{
									var ind = search_arr.indexOf('9');
									if(ind > -1){
										this.column(9).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f10').val() != ''){
									this.column(10).search((($('#f10').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('10')){
										search_arr.push('10');
									}
								}else{
									var ind = search_arr.indexOf('10');
									if(ind > -1){
										this.column(10).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f11').val() != ''){
									this.column(11).search((($('#f11').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('11')){
										search_arr.push('11');
									}
								}else{
									var ind = search_arr.indexOf('11');
									if(ind > -1){
										this.column(11).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f12').val() != ''){
									this.column(12).search((($('#f12').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('12')){
										search_arr.push('12');
									}
								}else{
									var ind = search_arr.indexOf('12');
									if(ind > -1){
										this.column(12).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f13').val() != ''){
									this.column(13).search($('#f13').val(),true,false).draw();
									if(!search_arr.includes('13')){
										search_arr.push('13');
									}
								}else{
									var ind = search_arr.indexOf('13');
									if(ind > -1){
										this.column(13).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}
								
							}
						},
						{text: '<input type="text" id="f2" placeholder="Reference Order" class="form-control">',className: ''},
						{text: '<input type="text" id="f3" placeholder="Destination City" class="form-control">',className: ''},
						{text: '<input type="text" id="f4" placeholder="Order Placed" class="form-control daterange-basic">'},
						{text: '<input type="text" id="f5"placeholder="Country" class="form-control">',className: ''},

						{text: '<input type="text" id="f6" placeholder="Pickup Location" class="form-control">',className: ''},
						{text: '<input type="text" id="f7" placeholder="Status" class="form-control">',className: ''},
						{text: '<select id="f13" class="form-control"> <option value="">All (COD Status)</option><option value="1">COD</option><option value="0">PAID</option> </select>',className: ''},
						{text: '<input type="text" id="f8" placeholder="Grand Total" class="form-control">',className: ''},

                        @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                            {text: '<input type="text" id="f9" placeholder="Customer Name" class="form-control">',className: ''},

                            {text: '<input type="text" id="f10" placeholder="Customer Email" class="form-control">',className: ''},
                            {text: '<input type="text" id="f11" placeholder="Customer Number" class="form-control">',className: ''},
						
                        @endif
                        
                        {text: '<input type="text" id="f12" placeholder="Tag" class="form-control">',className: ''},

						
					]
				},
			],
			scrollX: false,
			"serverSide": true,
			"ajax": "/seller/order/all_fulfilled",
			columnDefs: [ {
				orderable: false,
				className: 'select-checkbox',
				targets:   0,
			} ,
			{ width: '100%', targets: 1 },
			{
				targets: [2,3,4,5,6,7,8,9,10,11,12,13],
				searchable: true,
				visible: false,
				orderable: false
			}
			],
			"pagingType": "numbers",
			columns: [
				
				{ render: function(data, type, row) {
						return (' '); 
					}
				},
				
				{ data: 'id',name: 'id', render: function(data, type, row) {
					var temp = '';
					var pick_loc = '';
					row.tag.forEach(element => {
							temp+='<span class="label '+element.color+'">'+element.value+'</span>';
						});

						if(row.location_name == 'Undefined' || row.location_name == 'undefined'){
							pick_loc = 'No Location assigned';
						} else{
							pick_loc =  row.location_name;
						}

                        @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                            return ('<div style=""><div style="float:left;text-align:left;width:60%"><a target="_blank" style="font-size:18px;" href="order/'+data+'"># '+row.marketplace_reference_id+'</a> <br> <span class="label label-success">'+row.status+'</span> <br><br><i class="icon-calendar3"></i> '+(row.created_date?moment(row.created_date.date).format('YYYY-MM-DD HH:mm:ss'):'-')+'<br><i class="icon-person"></i> '+row.customer_name+'<br><i class="icon-mobile"></i> '+row.customer_number+'<br><i class="icon-location3"></i> '+row.destination_city+' - '+row.country+'<br><i class="icon-store"></i> '+pick_loc+'<br><br>'+temp+'</div><div style="float:right;text-align:center;width:40%;"><span style="font-size:16px;font-weight:bold">'+row.currency+' '+row.grand_total+'</span><br>'+(row.cod_payment == true ? '<span class="label label-success">COD</span>' : '<span class="label label-danger">PAID</span>')+'</div></div>');
                        @else
                            return ('<div style=""><div style="float:left;text-align:left;width:60%"><a target="_blank" style="font-size:18px;" href="order/'+data+'"># '+row.marketplace_reference_id+'</a> <br> <span class="label label-success">'+row.status+'</span> <br><br><i class="icon-calendar3"></i> '+(row.created_date?moment(row.created_date.date).format('YYYY-MM-DD HH:mm:ss'):'-')+'<br><i class="icon-location3"></i> '+row.destination_city+' - '+row.country+'<br><i class="icon-store"></i> '+pick_loc+'<br><br>'+temp+'</div><div style="float:right;text-align:center;width:40%;"><span style="font-size:16px;font-weight:bold">'+row.currency+' '+row.grand_total+'</span><br>'+(row.cod_payment == true ? '<span class="label label-success">COD</span>' : '<span class="label label-danger">PAID</span>')+'</div></div>');
                        @endif
					}  
				},


				{ data: 'marketplace_reference_id',name: 'marketplace_reference_id'},
				
				{  data: 'destination_city', name: 'destination_city' },
				
				{  data: 'created_date', name: 'created_date'},
				{  data: 'country', name: 'country'},
				{  data: 'location_name', name: 'location_name' },
				{  data: 'status', name: 'status'},
				{  data: 'grand_total', name: 'grand_total' },
				{  data: 'customer_name', name: 'customer_name' },
				{  data: 'customer_email', name: 'customer_email' },
				{  data: 'customer_number', name: 'customer_number' },
				{  data: 'tag', name: 'tag'},
				{  data: 'cod_payment', name: 'cod_payment'},
				
			],
			"order": [[1, 'desc']],
			"rowCallback": function( row, data ) {
				if ( $.inArray(data.DT_RowId, fullfilledSelected) !== -1 ) {
					firstTableMobile.row(row).select();
				}
			}
		});
		secondTableMobile.columns().search( '' ).draw();



		


		$('#fulfilledTableMobile tbody').on('click', 'tr', function() {
			if($(this).hasClass('selected'))
			{
				secondTableMobile.row($(this)).deselect();
			} else {
				secondTableMobile.row($(this)).select();
			}
		});


		////////////////// Function Run on select ///////////
		secondTableMobile.on( 'select', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = secondTableMobile.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], fullfilledSelected);
					if (idIndex === -1) {
						fullfilledSelected.push( rows[index] );
					}
				}

			}
			$('.fulfilledTableMobileAction').html('( '+fullfilledSelected.length+' ) Action <span class="caret"></span>');
		});


		////////////////// Function Run on deselect ///////////
		secondTableMobile.on( 'deselect', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = secondTableMobile.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], fullfilledSelected);
					fullfilledSelected.splice( idIndex, 1 );
				}

			}
			if (fullfilledSelected.length == 0) {
				$('.fulfilledTableMobileAction').html(' Action <span class="caret"></span>');
			} else {
				$('.fulfilledTableMobileAction').html('( '+fullfilledSelected.length+' ) Action <span class="caret"></span>');
			}
		});
		

		var addTagLI = '<li onclick=addTag("second")><a >Add Tag</a></li>';
		var removeTagLI = '<li onclick=removeTag("second")><a class="removeTag">Remove Tag</a></li>';
		var exportOrderLI = '<li onclick=exportOrder("second")><a class="exportOrder"> Export Order Details</a></li>';
		$( "#fulfilledTableMobile_wrapper .datatable-header" ).append( '<div class="btn-group"><div class="dataTables_filter"><button type="button" class="btn btn-danger secondTableMobileAction dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button><ul class="dropdown-menu">'+addTagLI+removeTagLI+exportOrderLI+'</ul></div></div>' );
	}
	
	function thirdTable() {

		thirdTableShow = true;

		// Individual column searching with text inputs
		$('#thirdTable tfoot td').each(function () {
			var title = $(this).text();
			if (!$(this).attr('id') && title) {
				$(this).html('<input type="text" class="form-control input-sm" placeholder="Search '+title+'" />');
			}
		});

		/////////For Cancelled Table///////////
		var thirdTable = $('#thirdTable').DataTable( {
			lengthMenu: [
				[ 20,30,50,100, 500, 2000, -1],
				[ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
			],
			"serverSide": true,
			"ajax": "/seller/order/all_cancelled",
			columnDefs: [ {
				orderable: false,
				className: 'select-checkbox',
				targets:   1
			} ,
			{
				orderable: false,
				searchable: false,
				targets:   6
			}],
			columns: [
				{ data: 'marketplace_reference_id',name: 'marketplace_reference_id', render: function(data, type, row) {
						return ('<a target="_blank" href="order/'+row.id+'">'+data+'</a>');
					}  
				},
				{  render: function(data, type, row) {
						return (' ');
					}
				},
				{  data: 'status', name: 'status', render: function(data, type, row) {
						return '<td><span class="label label-danger">'+data+'</span></td>'
					}
				},
				{  data: 'tag', name: 'tag.value', orderable: false, render: function(data, type, row) {
						
						var temp = '';
						data.forEach(element => {
							temp+='<td><span class="label '+element.color+'">'+element.value+'</span></td>';
						})

						return temp;
					}
				},

                @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                    {  data: 'customer_name', name: 'customer_name' },
                    {  data: 'customer_number', name: 'customer_number' },
                    {  data: 'shipping_address', name: 'shipping_address', render:function(data, type, row){
                        return (data ? '<div style="width: 250px;overflow: hidden;text-overflow: ellipsis; ">'+data+'</div>' : '-');
                    } },
                    {  data: 'customer_email', name: 'customer_email' },

                @endif
				
				{  data: 'created_date', name: 'created_date', render:function(data, type, row){
					return (data)?moment(data.date).format('YYYY-MM-DD HH:mm:ss'):'-';
				} },
				{  data: 'location_name', name: 'location_name' ,render: function(data, type, row) {
						if(data == 'Undefined' || data == 'undefined'){
							return 'No Location assigned';
						} else{
							return data;
						}
					}},
				{  data: 'destination_city', name: 'destination_city' },
				{  data: 'country', name: 'country'},
				
				
				{  data: 'cod_payment', name: 'cod_payment', render: function(data, type, row) {
						return (data == 1 ? '<td><span class="label label-success">Yes</span></td>' : '<td><span class="label label-danger">No</span></td>');
					}
				},
				{  data: 'display_name', name: 'seller_payment_methods.id' },
				{  data: 'grand_total', name: 'grand_total' },
				
				{ data: 'order_cancellation_reason', name: 'order_cancellation_reason' , orderable: false, searchable: false , render: function(data, type, row) {
						return (data != null ? '<td><span class="label '+row.order_cancellation_reason.reason.color+'">'+row.order_cancellation_reason.reason.value+'</span></td>': '')
				} },
				{ data: 'order_cancellation_reason.created_at', name: 'order_cancellation_reason.created_at' , orderable: false, searchable: false , render: function(data, type, row) {
						return (data != null ? moment(row.order_cancellation_reason.created_at.date).format('YYYY-MM-DD HH:mm:ss') : '-')
				} },
				
			],
			order: [[8, 'desc']],
			"rowCallback": function( row, data ) {
				if ( $.inArray(data.DT_RowId, cancelledSelected) !== -1 ) {
					thirdTable.row(row).select();
				}
			}
		});
		thirdTable.columns().search( '' ).draw();


		thirdTable.columns().every( function () {
			var that = this;
			$('input', this.footer()).on('keyup change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							if (orderPlacedSearch) {
								var start = new Date (((this.value).split('-'))[0]);
								var end = new Date (((this.value).split('-'))[1]);
								var dateArray = new Array();
								while (start <= end) {
									start = start.addDays(1);
									dateArray.push((new Date (start)).toISOString().slice(0,10));
								}
								var range = dateArray.join('|');
								that.search(range,true,false).draw();

							} else {
								that.search('',true,false).draw();
							}
							
						}, 500);

					} else {
						if((this.value).toLowerCase() == 'no location assigned'){
							that.search('undefined',true,false).draw();
						} else{

							// that.search(((this.value).trim()).replace(/\|$/, ''),true,false).draw();
							that.search(((this.value).trim()).replace(/ /g, '|'),true,false).draw();
						}
					}
				}
			});

			$('.select', this.footer()).on('change', function (e) {
				if (e.keyCode == 13 || $(this).attr('id')) {
					if ($(this).attr('id')) {
						setTimeout(() => {
							that.search(this.value,true,false).draw();
						}, 500);
					}
				}
			});
		});


		$('#thirdTable tbody').on('click', 'tr', function() {
			if($(this).hasClass('selected'))
			{
				thirdTable.row($(this)).deselect();
			} else {
				thirdTable.row($(this)).select();
			}
		});


		////////////////// Function Run on select ///////////
		thirdTable.on( 'select', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = thirdTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], cancelledSelected);
					if (idIndex === -1) {
						cancelledSelected.push( rows[index] );
					}
				}

			}
			$('.thirdTableAction').html('( '+cancelledSelected.length+' ) Action <span class="caret"></span>');
		});


		////////////////// Function Run on deselect ///////////
		thirdTable.on( 'deselect', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = thirdTable.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], cancelledSelected);
					cancelledSelected.splice( idIndex, 1 );
				}

			}
			if (cancelledSelected.length == 0) {
				$('.thirdTableAction').html(' Action <span class="caret"></span>');
			} else {
				$('.thirdTableAction').html('( '+cancelledSelected.length+' ) Action <span class="caret"></span>');
			}
		});

		var addTagLI = '<li onclick=addTag("third")><a >Add Tag</a></li>';
		var removeTagLI = '<li onclick=removeTag("third")><a class="removeTag">Remove Tag</a></li>';
		var exportOrderLI = '<li onclick=exportOrder("third")><a class="exportOrder"> Export Order Details</a></li>';
		$( "#thirdTable_wrapper .datatable-header" ).append( '<div class="dataTables_filter"><button type="button" class="btn btn-danger thirdTableAction dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button><ul class="dropdown-menu">'+addTagLI+removeTagLI+exportOrderLI+'</ul></div>' );
		
	}

	function thirdTableMobile() {

		thirdTableMobileShow = true;
		var search_arr = [];

		/////////For Cancelled Table///////////
		var thirdTableMobile = $('#cancelledTableMobile').DataTable( {
			lengthMenu: [
				[ 20,30,50,100, 500, 2000, -1],
				[ '20 rows', '30 rows', '50 rows', '100 rows', '500 rows', '2000 rows', 'All rows (Not Recommended)']
			],
			buttons: [
				{extend: 'selectAll',className: 'btn btn-info'},
				{extend: 'selectNone',className: 'btn btn-info'},
				{extend: 'pageLength',className: 'btn bg-slate-600'},
				{
					extend: 'collection',
					text: '<i class="icon-filter4"></i> <span class="caret"></span>',
					className: 'btn bg-danger',
					buttons: [
						{extend: 'copyHtml5',text: '<i class="icon-filter3"></i>&nbsp &nbsp Filter ',className: 'btn btn-default',
							action: function ( e, dt, node, config ) {
								if($('#f2').val() != ''){
									this.column(2).search((($('#f2').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('2')){
										search_arr.push('2');
									}
								} else if(search_arr.includes('2')){
									var ind = search_arr.indexOf('2');
									if(ind > -1){
										this.column(2).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f3').val() != ''){
									this.column(3).search((($('#f3').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('3')){
										search_arr.push('3');
									}
								}else{
									var ind = search_arr.indexOf('3');
									if(ind > -1){
										this.column(3).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f4').val() != ''){
									var start = new Date ((($('#f4').val()).split('-'))[0]);
									var end = new Date ((($('#f4').val()).split('-'))[1]);
									var dateArray = new Array();
									while (start <= end) {
										start = start.addDays(1);
										dateArray.push((new Date (start)).toISOString().slice(0,10));
									}
									var range = dateArray.join('|');
									this.column(4).search(range,true,false).draw();
									if(!search_arr.includes('4')){
										search_arr.push('4');
									}
								}
								else{
									var ind = search_arr.indexOf('4');
									if(ind > -1){
										this.column(4).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f5').val() != ''){
									this.column(5).search((($('#f5').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('5')){
										search_arr.push('5');
									}
								}
								else{
									var ind = search_arr.indexOf('5');
									if(ind > -1){
										this.column(5).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f6').val() != ''){
									if(($('#f6').val()).toLowerCase() == 'no location assigned'){
										this.column(6).search('undefined',true,false).draw();
									} else{
										this.column(6).search((($('#f6').val()).trim()).replace(/ /g, '|'),true,false).draw();
									}
									if(!search_arr.includes('6')){
										search_arr.push('6');
									}
								}
								else{
									var ind = search_arr.indexOf('6');
									if(ind > -1){
										this.column(6).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f7').val() != ''){
									this.column(7).search((($('#f7').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('7')){
										search_arr.push('7');
									}
								}else{
									var ind = search_arr.indexOf('7');
									if(ind > -1){
										this.column(7).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f8').val() != ''){
									this.column(8).search((($('#f8').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('8')){
										search_arr.push('8');
									}
								}else{
									var ind = search_arr.indexOf('8');
									if(ind > -1){
										this.column(8).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f9').val() != ''){
									this.column(9).search((($('#f9').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('9')){
										search_arr.push('9');
									}
								}else{
									var ind = search_arr.indexOf('9');
									if(ind > -1){
										this.column(9).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f10').val() != ''){
									this.column(10).search((($('#f10').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('10')){
										search_arr.push('10');
									}
								}else{
									var ind = search_arr.indexOf('10');
									if(ind > -1){
										this.column(10).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f11').val() != ''){
									this.column(11).search((($('#f11').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('11')){
										search_arr.push('11');
									}
								}else{
									var ind = search_arr.indexOf('11');
									if(ind > -1){
										this.column(11).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f12').val() != ''){
									this.column(12).search((($('#f12').val()).trim()).replace(/ /g, '|'),true,false).draw();
									if(!search_arr.includes('12')){
										search_arr.push('12');
									}
								}else{
									var ind = search_arr.indexOf('12');
									if(ind > -1){
										this.column(12).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								if($('#f13').val() != ''){
									this.column(13).search($('#f13').val(),true,false).draw();
									if(!search_arr.includes('13')){
										search_arr.push('13');
									}
								}else{
									var ind = search_arr.indexOf('13');
									if(ind > -1){
										this.column(13).search('',true,false).draw();
										search_arr.splice(ind,1);
									}
								}

								
							}
						},
						{text: '<input type="text" id="f2" placeholder="Reference Order" class="form-control">',className: ''},
						{text: '<input type="text" id="f3" placeholder="Destination City" class="form-control">',className: ''},
						{text: '<input type="text" id="f4" placeholder="Order Placed" class="form-control daterange-basic">'},
						{text: '<input type="text" id="f5"placeholder="Country" class="form-control">',className: ''},

						{text: '<input type="text" id="f6" placeholder="Pickup Location" class="form-control">',className: ''},
						{text: '<input type="text" id="f7" placeholder="Status" class="form-control">',className: ''},
						{text: '<select id="f13" class="form-control"> <option value="">All (COD Status)</option><option value="1">COD</option><option value="0">PAID</option> </select>',className: ''},
						{text: '<input type="text" id="f8" placeholder="Grand Total" class="form-control">',className: ''},

                        @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                            {text: '<input type="text" id="f9" placeholder="Customer Name" class="form-control">',className: ''},

                            {text: '<input type="text" id="f10" placeholder="Customer Email" class="form-control">',className: ''},
                            {text: '<input type="text" id="f11" placeholder="Customer Number" class="form-control">',className: ''},

                        @endif

                        {text: '<input type="text" id="f12" placeholder="Tag" class="form-control">',className: ''},

						
					]
				},
			],
			scrollX: false,
			"serverSide": true,
			"ajax": "/seller/order/all_cancelled",
			columnDefs: [ {
				orderable: false,
				className: 'select-checkbox',
				targets:   0,
			} ,
			{ width: '100%', targets: 1 },
			{
				targets: [2,3,4,5,6,7,8,9,10,11,12,13],
				searchable: true,
				visible: false,
				orderable: false
			}
			],
			"pagingType": "numbers",
			columns: [
				
				{ render: function(data, type, row) {
						return (' '); 
					}
				},
				
				{ data: 'id',name: 'id', render: function(data, type, row) {
					var temp = ''
					var pick_loc = '';
					row.tag.forEach(element => {
							temp+='<span class="label '+element.color+'">'+element.value+'</span>';
						});

						if(row.location_name == 'Undefined' || row.location_name == 'undefined'){
							pick_loc = 'No Location assigned';
						} else{
							pick_loc =  row.location_name;
						}

                        @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)
                            return ('<div style=""><div style="float:left;text-align:left;width:60%"><a target="_blank" style="font-size:18px;" href="order/'+data+'"># '+row.marketplace_reference_id+'</a> <br> <span class="label label-danger">'+row.status+'</span> <br><br><i class="icon-calendar3"></i> '+(row.created_date?moment(row.created_date.date).format('YYYY-MM-DD HH:mm:ss'):'-')+'<br><i class="icon-person"></i> '+row.customer_name+'<br><i class="icon-mobile"></i> '+row.customer_number+'<br><i class="icon-location3"></i> '+row.destination_city+' - '+row.country+'<br><i class="icon-store"></i> '+pick_loc+'<br><br>'+temp+'</div><div style="float:right;text-align:center;width:40%;"><span style="font-size:16px;font-weight:bold">'+row.currency+' '+row.grand_total+'</span><br>'+(row.cod_payment == true ? '<span class="label label-success">COD</span>' : '<span class="label label-danger">PAID</span>')+'</div></div>');
                        @else
                            return ('<div style=""><div style="float:left;text-align:left;width:60%"><a target="_blank" style="font-size:18px;" href="order/'+data+'"># '+row.marketplace_reference_id+'</a> <br> <span class="label label-danger">'+row.status+'</span> <br><br><i class="icon-calendar3"></i> '+(row.created_date?moment(row.created_date.date).format('YYYY-MM-DD HH:mm:ss'):'-')+'<br><i class="icon-location3"></i> '+row.destination_city+' - '+row.country+'<br><i class="icon-store"></i> '+pick_loc+'<br><br>'+temp+'</div><div style="float:right;text-align:center;width:40%;"><span style="font-size:16px;font-weight:bold">'+row.currency+' '+row.grand_total+'</span><br>'+(row.cod_payment == true ? '<span class="label label-success">COD</span>' : '<span class="label label-danger">PAID</span>')+'</div></div>');
                        @endif
					}  
				},


				{ data: 'marketplace_reference_id',name: 'marketplace_reference_id'},
				
				{  data: 'destination_city', name: 'destination_city' },
				
				{  data: 'created_date', name: 'created_date'},
				{  data: 'country', name: 'country'},
				{  data: 'location_name', name: 'location_name' },
				{  data: 'status', name: 'status'},
				{  data: 'grand_total', name: 'grand_total' },
				{  data: 'customer_name', name: 'customer_name' },
				{  data: 'customer_email', name: 'customer_email' },
				{  data: 'customer_number', name: 'customer_number' },
				{  data: 'tag', name: 'tag'},
				{  data: 'cod_payment', name: 'cod_payment'},
				
			],
			"order": [[1, 'desc']],
			"rowCallback": function( row, data ) {
				if ( $.inArray(data.DT_RowId, cancelledSelected) !== -1 ) {
					thirdTableMobile.row(row).select();
				}
			}
		});
		thirdTableMobile.columns().search( '' ).draw();


		


		$('#cancelledTableMobile tbody').on('click', 'tr', function() {
			if($(this).hasClass('selected'))
			{
				thirdTableMobile.row($(this)).deselect();
			} else {
				thirdTableMobile.row($(this)).select();
			}
		});


		////////////////// Function Run on select ///////////
		thirdTableMobile.on( 'select', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = thirdTableMobile.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], cancelledSelected);
					if (idIndex === -1) {
						cancelledSelected.push( rows[index] );
					}
				}

			}
			$('.cancelledTableMobileAction').html('( '+cancelledSelected.length+' ) Action <span class="caret"></span>');
		});


		////////////////// Function Run on deselect ///////////
		thirdTableMobile.on( 'deselect', function ( e, dt, type, indexes ) {
			
			if ( type === 'row' ) {
				
				var rows = thirdTableMobile.rows( indexes ).data().pluck( 'id' );

				for (let index = 0; index < rows.length; index++) {
					var idIndex = $.inArray(rows[index], cancelledSelected);
					cancelledSelected.splice( idIndex, 1 );
				}

			}
			if (cancelledSelected.length == 0) {
				$('.cancelledTableMobileAction').html(' Action <span class="caret"></span>');
			} else {
				$('.cancelledTableMobileAction').html('( '+cancelledSelected.length+' ) Action <span class="caret"></span>');
			}
		});

		var addTagLI = '<li onclick=addTag("third")><a >Add Tag</a></li>';
		var removeTagLI = '<li onclick=removeTag("third")><a class="removeTag">Remove Tag</a></li>';
		var exportOrderLI = '<li onclick=exportOrder("third")><a class="exportOrder"> Export Order Details</a></li>';
		$( "#cancelledTableMobile_wrapper .datatable-header" ).append( '<div class="btn-group"><div class="dataTables_filter"><button type="button" class="btn btn-danger thirdTableMobileAction dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button><ul class="dropdown-menu">'+addTagLI+removeTagLI+exportOrderLI+'</ul></div></div>' );
		
	}




	function addTag(tableName) {
		if (tableName == 'first') {
			if (pendingSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrder').val(pendingSelected);
				$('#modalSelectedOrder').html('There are <b style="color:green">'+pendingSelected.length+'</b> Orders Selected');
				$('#addTagOrder').modal('toggle');
			}
		} else if (tableName == 'all') {
			if (allSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrder').val(allSelected);
				$('#modalSelectedOrder').html('There are <b style="color:green">'+allSelected.length+'</b> Orders Selected');
				$('#addTagOrder').modal('toggle');
			}

		} else if (tableName == 'second') {
			if (fullfilledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrder').val(fullfilledSelected);
				$('#modalSelectedOrder').html('There are <b style="color:green">'+fullfilledSelected.length+'</b> Orders Selected');
				$('#addTagOrder').modal('toggle');
			}

		} else if (tableName == 'third') {
			if (cancelledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrder').val(cancelledSelected);
				$('#modalSelectedOrder').html('There are <b style="color:green">'+cancelledSelected.length+'</b> Orders Selected');
				$('#addTagOrder').modal('toggle');
			}

		} 
	}

	function removeTag(tableName) {
		if (tableName == 'first') {
			if (pendingSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderRemove').val(pendingSelected);
				$("#tagRemoveForm").submit();
			}
		} else if (tableName == 'all') {
			if (allSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderRemove').val(allSelected);
				$("#tagRemoveForm").submit();
			}
		} else if (tableName == 'second') {
			if (fullfilledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderRemove').val(fullfilledSelected);
				$("#tagRemoveForm").submit();
			}
		} else if (tableName == 'third') {
			if (cancelledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderRemove').val(cancelledSelected);
				$("#tagRemoveForm").submit();
			}

		} 
	}

	function reSendToDOM(tableName) {
		if (tableName == 'first') {
			if (pendingSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderToResend').val(pendingSelected);
				$("#reSendToDOMForm").submit();
			}
		} else if (tableName == 'all') {
			if (allSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderToResend').val(allSelected);
				$("#reSendToDOMForm").submit();
			}
		} else if (tableName == 'second') {
			if (fullfilledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderToResend').val(fullfilledSelected);
				$("#reSendToDOMForm").submit();
			}
		} else if (tableName == 'third') {
			if (cancelledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderToResend').val(cancelledSelected);
				$("#reSendToDOMForm").submit();
			}

		} 
	}
	
	function changeCity(tableName) {
		if (tableName == 'first') {
			if (pendingSelected.length < 1) {
				alert('First select an order');
			} else {
				$('#selectedOrderChangeCity').val(pendingSelected);
				$('#changeCityOrder').modal('toggle');
			}
		} else if (tableName == 'all') {
			if (allSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderChangeCity').val(allSelected);
				$('#changeCityOrder').modal('toggle');
			}
		} else if (tableName == 'second') {
			if (fullfilledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderChangeCity').val(fullfilledSelected);
				$('#changeCityOrder').modal('toggle');
			}
		} else if (tableName == 'third') {
			if (cancelledSelected.length < 1) {
				alert('first select an order');
			} else {
				$('#selectedOrderChangeCity').val(cancelledSelected);
				$('#changeCityOrder').modal('toggle');
			}

		} 
	}
	
	function changeLocation(tableName) {
		if (tableName == 'first') {
			if (pendingSelected.length < 1) {
				alert('First select an order');
			} else {
				$('#selectedOrderChangeLocation').val(pendingSelected);
				$('#changeLocationOrder').modal('toggle');
			}
		} 
		else if (tableName == 'all') {
			if (allSelected.length < 1) {
				alert('First select an order');
			} else {
				$('#selectedOrderChangeLocation').val(allSelected);
				$('#changeLocationOrder').modal('toggle');
			}
		} 
	}
	var authId = 
			@if(session()->has('user'))
				{!! json_encode(session('user')->id ?? null) !!}
			@else
				{!! Auth::user()->id !!}
			@endif
		;
	function roboCall(tableName) {
		if (tableName == 'first') {
			if (pendingSelected.length < 1) {
				alert('First select an order');
			} else {
				$.ajax({
					type: "POST",
					url: '{{ url('/api/robocall/') }}',
					data: {ids: pendingSelected, auth_id: authId },
					success: function(msg) {
						alert(msg);
					}
				});
			}
		} 
		else if (tableName == 'all') {
			if (allSelected.length < 1) {
				alert('First select an order');
			} else {
				$.ajax({
					type: "POST",
					url: '{{ url('/api/robocall/') }}',
					data: {ids: allSelected, auth_id: authId},
					success: function(msg) {
						alert(msg);
					}
				});
			}
		} 
	}

	function smsTrigger(tableName) {
		if (tableName == 'first') {
			if (pendingSelected.length < 1) {
				alert('First select an order');
			} else {
				notify('SMS Dispatch Processing',' ','icon-spinner9 spinner','bg-info',false);
				$.ajax({
					type: "POST",
					url: '{{ url('seller/order/sms-trigger') }}',
					data: { "_token": "{{ csrf_token() }}",ids: pendingSelected },
					success: function(msg) {
						removeNotify();
						alert('SMS Dispatch Process Complete');
					}
				});
			}
		} 
		else if (tableName == 'all') {
			if (allSelected.length < 1) {
				alert('First select an order');
			} else {
				notify('SMS Dispatch Processing',' ','icon-spinner9 spinner','bg-info',false);
				$.ajax({
					type: "POST",
					url: '{{ url('seller/order/sms-trigger') }}',
					data: { "_token": "{{ csrf_token() }}",ids: allSelected },
					success: function(msg) {
						removeNotify();
						alert('SMS Dispatch Process Complete');
					}
				});
			}
		} 
	}


	function exportOrder(tableName){
        
		if(tableName == 'first'){
			if (pendingSelected.length > 0) {

				notify('Exporting Order Details',' ','icon-spinner9 spinner','bg-info',false);
				
				window.open(
					'export-order/?ids='+pendingSelected,
					'_blank' // <- This is what makes it open in a new window.
				);

				removeNotify();

			} else {
				alert('Select the Order First');
			}
		} else if(tableName == 'all'){
			if (allSelected.length > 0) {

			notify('Exporting Order Details',' ','icon-spinner9 spinner','bg-info',false);

			window.open(
				'export-order/?ids='+allSelected,
				'_blank' // <- This is what makes it open in a new window.
			);

			removeNotify();

			} else {
				alert('Select the Order First');
			}
		} else if(tableName == 'second'){
			if (fullfilledSelected.length > 0) {

			notify('Exporting Order Details',' ','icon-spinner9 spinner','bg-info',false);

			window.open(
				'export-order/?ids='+fullfilledSelected,
				'_blank' // <- This is what makes it open in a new window.
			);

			removeNotify();

			} else {
				alert('Select the Order First');
			}
		} else if(tableName == 'third'){
			if (cancelledSelected.length > 0) {

				notify('Exporting Order Details',' ','icon-spinner9 spinner','bg-info',false);

				window.open(
					'export-order/?ids='+cancelledSelected,
					'_blank' // <- This is what makes it open in a new window.
				);

				removeNotify();

			} else {
				alert('Select the Order First');
			}
		}
	}


	function invoice(tableName){
        
		if(tableName == 'first'){
			if (pendingSelected.length > 0) {

				notify('Printing Order Invoices',' ','icon-spinner9 spinner','bg-info',false);
				
				$.ajax({
				url: '{!! url('seller/order/print_invoice') !!}',
                type: 'post',
				data: {
                    "_token": "{{ csrf_token() }}",
					id:pendingSelected
				}
			})
			.done(function(data) {
				var win = window.open('', '_blank');
				
				if (win) {
					data.forEach(function(element,index){
						
						$(win.document.body).html(element);
						if (data.length-1 != index ) {
							win = window.open('', '_blank');
						}
						
					});
					
					win.focus();
					win.print();
					
				} else {
					alert('Please allow popups for Unity');
				}
				removeNotify();
			});

			} else {
				alert('Select the Order First');
			}
		} else if(tableName == 'all'){
			if (allSelected.length > 0) {

				notify('Printing Order Invoices',' ','icon-spinner9 spinner','bg-info',false);

				$.ajax({
				url: '{!! url('seller/order/print_invoice') !!}',
                type: 'post',
				data: {
                    "_token": "{{ csrf_token() }}",
					id:allSelected
				}
				})
				.done(function(data) {
				var win = window.open('', '_blank');

				if (win) {
					data.forEach(function(element,index){
						
						$(win.document.body).html(element);
						if (data.length-1 != index ) {
							win = window.open('', '_blank');
						}
						
					});
					
					win.focus();
					win.print();
					
				} else {
					alert('Please allow popups for Unity');
				}
				removeNotify();
				});

			} else {
				alert('Select the Order First');
			}
		}
	}

	var activeTable = ''

	function bulkCancelAlert(tableName)
	{
		if (tableName == 'first') {
			if (pendingSelected.length !== 0) {
				activeTable = 'first';
				if(reasons_count > 0)
				{
					$('#modal_cancel_confirm_with_reason').modal('show');
				}
				else{
					$('#modal_cancel_confirm_without_reason').modal('show');
				}
			} else {
				alert('Select the Shipment First');
			}
			
		} 
		else if (tableName == 'all') {
			if (allSelected.length !== 0) {
				activeTable = 'all';
				if(reasons_count > 0)
				{
					$('#modal_cancel_confirm_with_reason').modal('show');
				}
				else{
					$('#modal_cancel_confirm_without_reason').modal('show');
				}
			} else {
				alert('Select the Shipment First');
			}
			
		} 
	}

	$('#accept_bulk_cancel').click(function()
	{
		console.log('dd')
		$(this).attr('disabled',true);
		$('#modal_cancel_confirm_with_reason').modal('hide');
		$('.firstTableAction').attr('disabled',true);
		bulkCancellation();
	})


	function bulkCancellation()
	{
		if(activeTable == 'first'){
			notify('Cancelling '+pendingSelected.length+' Orders(s)',' ','icon-spinner9 spinner','bg-info',false);
			var reason_selected = $('#reasons_selected').val();
			$.ajax({
				url: '{!! url('seller/order/bulk_cancellation') !!}',
				data: {
					ids: pendingSelected,
					reason: reason_selected
				}
			})
			.done(function(data) {
				removeNotify();
				if(data['error'] == 0)
				{
					notify(data['message'],' ','icon-spinner9 spinner','bg-success',false);
					setTimeout(function()
					{
						location.reload();
					},2000);
				}
				else{
					
					notify(data['message'],' ','icon-spinner9 spinner','bg-danger',false);
				}

				
				
			});
		} else if(activeTable == 'all'){
			notify('Cancelling '+allSelected.length+' Orders(s)',' ','icon-spinner9 spinner','bg-info',false);
			var reason_selected = $('#reasons_selected').val();
			$.ajax({
				url: '{!! url('seller/order/bulk_cancellation') !!}',
				data: {
					ids: allSelected,
					reason: reason_selected
				}
			})
			.done(function(data) {
				removeNotify();
				if(data['error'] == 0)
				{
					notify(data['message'],' ','icon-spinner9 spinner','bg-success',false);
					setTimeout(function()
					{
						location.reload();
					},2000);
				}
				else{
					
					notify(data['message'],' ','icon-spinner9 spinner','bg-danger',false);
				}

				
				
			});
		}

	}



	$('.all').on('click', function() {
		$('#cancelledTable').hide();
		$('#fulfilledTable').hide();
		$('#pendingTable').hide();
		$('#allTable').show();

		$('#cancelledTableMobile').hide();
		$('#fulfilledTableMobile').hide();
		$('#pendingTableMobile').show();

		animation('lightSpeedIn','#allTable');
		animation('lightSpeedIn','#pendingTableMobile');
	});

	$('.pending').on('click', function() {

		if (!firstTableShow) {
			firstTable();
		}

		$('#allTable').hide();
		$('#cancelledTable').hide();
		$('#fulfilledTable').hide();
		$('#pendingTable').show();

		$('#cancelledTableMobile').hide();
		$('#fulfilledTableMobile').hide();
		$('#pendingTableMobile').show();

		animation('lightSpeedIn','#pendingTable');
		animation('lightSpeedIn','#pendingTableMobile');
	});

	$('.fulfilled').on('click', function() {

		if (!secondTableShow) {
			secondTable();
			secondTableMobile();
		}
		
		
		$('#fulfilledTable').show();
		$('#pendingTable').hide();
		$('#cancelledTable').hide();
		$('#allTable').hide();

		$('#fulfilledTableMobile').show();
		$('#pendingTableMobile').hide();
		$('#cancelledTableMobile').hide();

		animation('lightSpeedIn','#fulfilledTable');
		animation('lightSpeedIn','#fulfilledTableMobile');
	});

	$('.cancelled').on('click', function() {

		if (!thirdTableShow) {
			thirdTable();
			thirdTableMobile();
		}

		$('#cancelledTable').show();
		$('#pendingTable').hide();
		$('#fulfilledTable').hide();
		$('#allTable').hide();

		$('#cancelledTableMobile').show();
		$('#pendingTableMobile').hide();
		$('#fulfilledTableMobile').hide();

		animation('lightSpeedIn','#cancelledTable');
		animation('lightSpeedIn','#cancelledTableMobile');	
	});

	$("#multipleTagForm").on("submit", function(){
		$("#multipleTagSubmitButton").attr('disabled',true);
		console.log('submit');
	});



	function animation(animation,params) {
		var animation =  animation;
		$(params).addClass("animated " + animation).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend", function () {
			$(params).removeClass("animated " + animation);
		});
	}


</script>
@endsection