
@extends('seller.admin_template')

<style>
  #total_cod {
    font-weight: bold;
  }

  #total_cod span {
    display: inline-block;
    margin-left: 10px;
    padding: 5px 10px;
    background: #eee;
    text-decoration: underline;
    border: 1px solid #d2d6de;
  }

  .badge_silver{
        background-color: silver;
    }

    .badge_gold{
        background-color: #D4AF37;
    }
</style>

@section('content')
	<meta name="csrf-token" content="{{ csrf_token() }}">

	<link href="{{ asset ("assets/css/extras/animate.min.css") }}" rel="stylesheet" type="text/css">

	<script type="text/javascript" src="{{ asset ("assets/js/plugins/buttons/ladda.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/pages/components_buttons.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/css/icons/fontawesome/styles.min.css") }}"></script>
	
	<script src="{{ asset ("assets/js/pages/form_layouts.js ") }}"></script>

	@php
			$currency = $order->currency.' ';
	@endphp

	@include('seller.notification')

		<!-- Vertical form modal -->
		<div id="modal_cancel_confirm" class="modal fade">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">&times;</button>
						<h3 class="modal-title">Cancel Order</h3>
					</div>

					<form action="/seller/order/cancelledOrder" method="POST" enctype="multipart/form-data">
						{{ csrf_field() }}
						<div class="modal-body">
							
							<h6>Are you sure you want to cancel the order? This is irreversible!</h6>
							<input type="hidden" class="form-control" id="order_id" name="id">
							<br><br>
							<label><b>Select Cancellation Reason</b><b style="color:red"> *</b></label>
							<select name="reason" data-placeholder="Select Cancellation Reason" class="select" required>
								@foreach ($reasons as $reason)
									<option value="{{$reason->id}}">{{$reason->value}}</option>
								@endforeach
							</select>
							

							
						</div>

						<div class="modal-footer">
							<button type="button" class="btn btn-link" data-dismiss="modal">Don't Cancel</button>
							<button type="submit" class="btn btn-danger">Cancel Order</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- /vertical form modal -->


		<!-- Modal -->
		<div class="modal fade modal-danger" id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<form id="cancelItem" action="/seller/order/cancelItems" method="post">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel">Refuse Items</h4>
						</div>
						<div class="modal-body">
							<div class="row">
								<div class="col-xs-12 table-responsive">
									<table class="table table-condensed" id="cancels">
										<thead>
											<tr>
												<th>Select</th>
												<th>SKU</th>
												<th>Name</th>
												<th>Description</th>
												<th>Unit Price</th>
												<th>QTY</th>
												<th>Subtotal</th>
											</tr>
										</thead>
										<tbody>
											@foreach ($order->items as $item)
												@if($item->status=="Pending")
													<tr>
														<td><input type="checkbox" name="select[]" value="{{ $item->id }}"></td>
														<td>{{ $item->SKU }}</td>
														<td>{{ $item->product_name }}</td>
														<td>{{ $item->description }}</td>
														<td>{{ $currency .number_format($item->unit_price,0) }}</td>
														<td>{{ $item->quantity }}</td>
														<td>{{ $currency .number_format($item->sub_total,0) }}</td>
													</tr>
												@endif
											@endforeach
										</tbody>
									</table>
								</div>
								<!-- /.col -->
							
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-4 control-label">Reason:</label>
									<div class="col-sm-6">
										<input type="text" class="form-control" name="reason" id="inputEmail3" required>
									</div>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
							<button type="submit" class="btn btn-primary" id="cancel_submit">Save changes</button>
						</div>
						<input type="hidden" name="order_id" value="{{ $order->id }}" />
						{{ csrf_field() }}
					</form>
				</div>
			</div>
		</div>

			<div class="row">
				<div class="col-md-12">
					<!-- Invoice template -->
					<div class="panel panel-primary">
						<div class="panel-heading">
							<h3 class="panel-title"><b>Order # {{ $order->marketplace_reference_id }}  </b> | <b> {{ $order->status }}</b></h3>
							<div class="heading-elements visible-elements">
								
								{{-- Order Tags --}}
								<div style="margin:0px" class="btn-group btn-group-animated">
									@if ( (!session()->has('permission') || session('permission')->order_tagging))
									<button style="color: {{(count($order->tag) > 0 ? 'white' : 'black')}} " type="button" class="btn {{(count($order->tag) > 0 ? $order->tag[0]->color : 'btn-grey')}}  btn-xs heading-btn dropdown-toggle " data-toggle="dropdown"><i class="icon-price-tag2 position-left"></i> {{(count($order->tag) > 0  ? $order->tag[0]->value : 'Select Tag')}} <span class="caret"></span></button>
									@else
									<button style="color: {{(count($order->tag) > 0 ? 'white' : 'black')}} " type="button" class="btn {{(count($order->tag) > 0 ? $order->tag[0]->color : 'btn-grey')}}  btn-xs heading-btn dropdown-toggle " ><i class="icon-price-tag2 position-left"></i> {{(count($order->tag) > 0  ? $order->tag[0]->value : 'Select Tag')}} <span class="caret"></span></button>
									@endif
									<ul class="dropdown-menu" style="width:50%; max-height: 400px;overflow: auto">
										
										@foreach($tag as $value)
											<li><a href="tag/{{$order->id}}/{{$value->id}}">{{$value->value}}</a></li>
										@endforeach
										@if(count($order->tag) > 0)
											<hr>
											<li><a href="tagRemove/{{$order->id}}">Remove Tag</a></li>
										@endif
				
									</ul>
								</div>
								
								{{-- Order Tags --}}

								{{-- Action --}}
								<div style="margin:0px" class="btn-group btn-group-animated">
									<button type="button" class="btn btn-danger btn-xs heading-btn" dropdown-toggle" data-toggle="dropdown"><b>Action</b> <span class="caret"></span></button>
									<ul class="dropdown-menu">

									@if(session()->has('permission'))
										@if ($roboCall && session('permission')->robocall == 1)
											<li><a class="roboCall" data-id="{{ $order->id }}"><i class="icon-phone-outgoing"></i> Robo Call</a></li>
										@endif
									@else
										@if ($roboCall)
											<li><a class="roboCall" data-id="{{ $order->id }}"><i class="icon-phone-outgoing"></i> Robo Call</a></li>
										@endif
									@endif

										@if($order->status == 'Pending')
											@if(session()->has('permission'))

												@if(session('permission')->order_cancel == 1)
													<li><a class="cancelledOrder" data-id="{{ $order->id }}"><i class="icon-blocked"></i> Cancelled</a></li>
												@endif
											
												@if(session('permission')->order_edit == 1)
													<li><a href="{{ $order->id }}/edit" data-id="{{ $order->id }}"><i class="icon-pencil"></i> Edit</a></li>
												@endif
											
											@else
												<li><a class="cancelledOrder" data-id="{{ $order->id }}"><i class="icon-blocked"></i> Cancelled</a></li>
												<li><a href="{{ $order->id }}/edit" data-id="{{ $order->id }}"><i class="icon-pencil"></i> Edit</a></li>

											@endif
										@endif

										<li><a class="print_invoice" data-id="{{ $order->id }}"><i class="icon-printer"></i> Print Invoice</a></li>

										
										@unless($order->status == 'Completed') 

											<li><a data-toggle="modal" data-target="#cancelModal"><i class="icon-blocked"></i> Refuse Items</a></li>

											@if(session()->has('permission'))
												@if(session('permission')->order_process == 1)
													@if($city_id)
														<li><a class="manual_dispatch" data-id="{{ $order->id }}"><i class="icon-credit-card"></i> Manual CN Assignment</a></li>
													@endif
												@endif
											@else
												@if($city_id)
													<li><a class="manual_dispatch" data-id="{{ $order->id }}"><i class="icon-credit-card"></i> Manual CN Assignment</a></li>
												@endif 
											@endif

										@endunless

										@if ($passthrough)
											<li><a href="{{$order->id}}/passthrough"><i class="icon-transmission"></i> Passthrough</a></li>
										@endif

										@if ($fbr && !$order->fbr_invoice_no)
											<!-- <li><a href="{{$order->id}}/fbr-request"><i class="icon-file-text"></i> Send FBR Request</a></li> -->
										@endif

										@if ($omniLocationAssignment)
											<li><a href="{{$order->id}}/resend-to-dom"><i class="icon-reset"></i> Re-send to DOM</a></li>
										@endif
				
									</ul>
								</div>
								{{-- Action --}}

								{{-- Book Shipment --}}
								@unless($order->status == 'Completed') 

									@if(!$ffc)
										@if(session()->has('permission'))
											@if(session('permission')->order_process == 1)
												@if($city_id)
													<button type="button" data-id="{{ $order->id }}" class="btn btn-success btn-xs heading-btn   book_shipment_button"><i class="icon-truck position-left" ></i> Book Shipment</button>
												@endif
											@endif
										@else
											@if($city_id)
												<button type="button" data-id="{{ $order->id }}" class="btn btn-success btn-xs heading-btn   book_shipment_button"><i class="icon-truck position-left" ></i> Book Shipment</button>
											@endif
										@endif
									@endif
								@endunless
								{{-- Book Shipment --}}

						</div>
					</div>

					<div class="panel-body no-padding-bottom">

						<div class="row">
							

							<div class="col-md-6 col-lg-9 content-group">
								@if (isset($channel->value) && isset($order->marketplace_id) && isset($wc->value) && $channel->value == 'shopify') <a href="https://admin.shopify.com/store/{{str_replace('.myshopify.com', '', $wc->value)}}/orders/{{$order->marketplace_id}}" target="_blank"><button type="button" class="btn border-success text-success btn-flat">Open in Shopify</button></a> @endif
								@if (isset($channel->value) && isset($order->marketplace_id) && isset($order->customer_reference_id) && isset($wc->value) && $channel->value == 'shopify') <a href="https://{{$wc->value}}/admin/customers/{{$order->customer_reference_id}}" target="_blank"><button type="button" class="btn border-success text-success btn-flat">Open Customer in Shopify</button></a> @endif
								
								@if(isset($order->customer_number) && $order->customer_number != '' && $order->customer_number != ' ')
									@php
										try{
											$phoneNumberUtil = \libphonenumber\PhoneNumberUtil::getInstance();
											$phoneNumberObject1 = $phoneNumberUtil->parse($order->customer_number, $order->country);
											$phone_number = $phoneNumberUtil->format($phoneNumberObject1,\libphonenumber\PhoneNumberFormat::E164);
										} catch(\Exception $e){
											$phone_number = '';
										}
									@endphp
									<a href="https://web.whatsapp.com/send?phone={{$phone_number}}" target="_blank"><button type="button" class="btn btn-success btn-raised legitRipple"><i class="fa fa-whatsapp position-left" style="font-size: 14px"></i> Chat On Whatsapp</button></a>
								@endif
								

								<ul class="list-condensed list-unstyled">
									
									<li><h6><b>From Channel : </b> @if($order->source) {{ $order->source->name }} @endif </h6></li>
									<li><h6><b>From Pickup Location : </b> @if(isset($order->location->location_name)) {{ $order->location->location_name }} @endif </h6></li>
									<li><h6><b>Placement Date : </b> {{ $order->placement_date }}</h6></li>
									<li><h6><b>Created Date : </b> {{ $order->created_date->toDayDateTimeString() }}</h6></li>
									@if ($fbr && $order->fbr_invoice_no)
										<li><h6><b>FBR Invoice No : </b> {{ $order->fbr_invoice_no }}</h6></li>
									@endif
									<li>
										<div class="content-group">
											<h4><b>Payment Method :</b> 
											@if(isset($payment_method_name))
											{{ $payment_method_name }}
											@else
											{{ $order->cod_payment? 'Cash On Delivery': 'Paid'}}
											@endif
											</h4>
											{{-- <div class="mb-15 mt-15">
												<img src="{{ $order->cod_payment? '/img/thumb-img/cod.svg': '/img/thumb-img/paid.png'}}" class="display-block" style="width: 150px;" alt="">
	
											</div> --}}
										</div>
									</li>
									@if ($order->storefront_payment_id && $order->storefront_payment_id != NULL)
										<li><h6><b>Payment ID : </b> {{ $order->storefront_payment_id }}</h6></li>
									@endif
									@isset($shipment_method_name)
										<li>
											<div class="content-group">
												<h4><b>Shipping Method :</b> 
												{{ $shipment_method_name }}
												</h4>
											</div>
										</li>
									@endisset
								</ul>
							</div>

							<div class="col-md-6 col-lg-3 content-group">
								<ul class="list-condensed list-unstyled invoice-payment-details">
									<li><h4><b>Customer Details: </b></h4></li>

                                    @if (!session()->has('permission') || session('permission')->data_privacy_view_pii)

                                        <li><h6><b>Name: </b><span class="text-semibold">{{ $order->customer_name ?? '-' }}</span></li>
                                        <li><b>Address: </b><span>{{ $order->shipping_address ?? '-' }}</span></li>
                                        <li><b>Postal Code: </b><span>{{ $order->postal_code ?? '-' }}</span></li>
                                        <li><b>City: </b><span>{{ $order->destination_city ?? '-' }}</span></li>
                                        @if($order->region) <li><b>State / Province: </b><span>{{ $order->region }}</span></li> @endif
                                        <li><b>Country: </b><span>{{ $order->country ?? '-' }}</span></li>
                                        <li><b>Email: </b><span>{{ $order->customer_email ?? '-'}}</span></li>
                                        <li><b>Phone: </b><span class="text-semibold">{{ $order->customer_number ?? '-'}}&nbsp;&nbsp;@if($blacklist == true) <span style="background-color:#f44336; color:#ffff" class="label">Blacklist</span> @endif</span></li>
									
                                    @else

                                        <li><b>City: </b><span>{{ $order->destination_city ?? '-' }}</span></li>
                                        @if($order->region) <li><b>State / Province: </b><span>{{ $order->region }}</span></li> @endif
                                        <li><b>Country: </b><span>{{ $order->country ?? '-' }}</span></li>


                                    @endif
                                    
                                    <li><b>Coordinates: </b><span class="text-semibold">{{ $order->customer_lat ? $order->customer_lat.',':'-/'}}{{ $order->customer_long ?? '-'}}</span></li>
									
									@if ($badge == 3)
										<li><b>Badge: </b><span class="label badge_silver">SILVER</span></li>
									@elseif($badge == 2)
										<li><b>Badge: </b><span class="label badge_gold">GOLD</span></li>
									@elseif($badge == 1)
										<li><b>Badge: </b><span class="label badge_silver">SILVER</span></b><span class="label badge_gold">GOLD</span></li>
									@elseif($badge == 0)
										
									@endif

									<li><b>Return Ratio: </b><span class="text-semibold">{{  $order->customer_stats ? number_format($order->customer_stats->return_ratio,2).' %' : '-' }} </span></b></li>

								</ul>
							</div>
						</div>
					</div>

					<div class="table-responsive">
						<table class="table table-lg" style="font-size:medium">
							<thead class="bg-indigo-400">
								<tr>
									<th><b>ID</b></th>
									<th><b style="padding-right:70px">SKU</b></th>
									<th><b>Barcode</b></th>
									<th><b style="padding-right:80px">Name</b></th>
									<th><b>Category</b></th>
									<th><b style="padding-right:70px">Description</b></th>
									<th><b>Unit Weight</b></th>
									<th><b>Unit Price</b></th>
									<th><b>QTY</b></th>
									<th><b>Discount</b></th>
									<th><b>Subtotal</b></th>
									<th><b>Total Tax</b></th>
									<th><b>Tracking Number</b></th>
									<th><b>Seller Location</b></th>
									@php($ffc = \App\Models\AddOn::ffc( Auth::id() ))
									@if($ffc)
										<th><b>Picked</b></th>
										<th><b>Packed</b></th>
									@endif
									<th><b>Status</b></th>
								</tr>
							</thead>
							<tbody style="font-size:12px">
								@foreach ($order->items as $item)
									<tr>
									<td>{{ $item->id }}</td>
									<td>{{ $item->SKU }}</td>
									<td>{{ $item->barcode }}</td>

									<td>{{ (json_decode($item->product_name) != null ? json_decode($item->product_name) : $item->product_name) }}</td>

                                    <td>{{ $item->category }}</td>
									<td>{!! json_decode($item->description) && json_last_error() === JSON_ERROR_NONE ? json_decode($item->description) : $item->description !!}</td>
									<td>{{ $item->weight }}</td>
									<td>{{ $currency }} @formatFloat($item->unit_price)</td>
									<td>{{ $item->quantity }}</td>
									<td>{{ $currency }} @formatFloat($item->discount) </td>
									<td>{{ $currency }} @formatFloat($item->sub_total) </td>
									<td>{{ $currency }} @formatFloat($item->tax) </td>
									<td>
										@if(App\Models\ShipmentItem::where('order_items_id',$item->id)->get()->first() && $item->status != config('enum.item_status')['PENDING'])
											<a target="_blank" href="/seller/shipment/{{App\Models\ShipmentItem::where('order_items_id',$item->id)->get()->last()->shipment->id}}"> {{App\Models\ShipmentItem::where('order_items_id',$item->id)->get()->last()->shipment->tracking_number}}</a>
										@endif
									</td>
									<td>{{ $item->fulfillment_item_latest && $item->fulfillment_item_latest->fulfillment_order->status != config('enum.fulfillment_order_status')['REJECT'] ? ( !($item->fulfillment_item_latest->fulfillment_order->is_dispatched) && $item->fulfillment_item_latest->fulfillment_order->status == config('enum.fulfillment_order_status')['CLOSED'] ? '-' : $item->fulfillment_item_latest->fulfillment_order->location->location_name ) : '-' }}</td>

									@if($ffc)
										<td>
											@if ($item->fulfillment_item_latest && $item->fulfillment_item_latest->fulfillment_order->status != config('enum.fulfillment_order_status')['REJECT'] && $item->fulfillment_item_latest->fulfillment_order->is_picked)

												@if (!($item->fulfillment_item_latest->fulfillment_order->is_dispatched) && $item->fulfillment_item_latest->fulfillment_order->status == config('enum.fulfillment_order_status')['CLOSED'])
													<span class="label label-danger">No</span>
												@else
													<span class="label label-success">Yes</span>
												@endif

											@else
												<span class="label label-danger">No</span>											
											@endif
										</td>
										<td>
											@if ($item->fulfillment_item_latest && $item->fulfillment_item_latest->fulfillment_order->status != config('enum.fulfillment_order_status')['REJECT'] && $item->fulfillment_item_latest->fulfillment_order->is_packed)
												
												@if (!($item->fulfillment_item_latest->fulfillment_order->is_dispatched) && $item->fulfillment_item_latest->fulfillment_order->status == config('enum.fulfillment_order_status')['CLOSED'])
													<span class="label label-danger">No</span>
												@else
													<span class="label label-success">Yes</span>
												@endif
											@else
												<span class="label label-danger">No</span>											
											@endif
										</td>
									@endif

									<td style="color: 
										@if($item->status == 'Cancelled' || $item->status == 'Return')
											red
										@elseif($item->status == 'Completed')
											green
										@elseif($item->status == 'Dispatched')
											#5f51a3
										@endif
									">{{ $item->status }}</td>
									</tr>
								@endforeach
							</tbody>
						</table>
					</div>

					<div class="panel-body">
						<div class="row invoice-payment">
							
							<div class="col-sm-5">


								<br><br>
								<!-- Add Comments -->
								<div class="panel-group content-group" id="menu-accordion">
									{{-- <div class="panel panel-success">
										<a data-toggle="collapse" data-parent="#menu-accordion" href="#accordion1">
										<div class="panel-heading" style="color: white; background-color: #4caf4f;">
											<h6 class="panel-title text-semibold">
												<i class="icon-truck position-left"></i> Book Shipment
											</h6>
										</div>
										</a>

										<div id="accordion1" class="panel-collapse collapse in">
											<div class="panel-body">
												
											</div>
										</div>
									</div> --}}
									@if ( (!session()->has('permission') || session('permission')->order_comments))

									<div class="panel panel-primary">
										<div class="panel-heading">
											<h6 class="panel-title text-semibold">
												<a data-toggle="collapse" data-parent="#menu-accordion" href="#accordion2"><i class="icon-plus3 position-left"></i>Add Comments</a>
											</h6>
										</div>

										<div id="accordion2" class="panel-collapse collapse in">
											<div class="panel-body">
												
												<form action="./{{$order->id}}/add_comment" method="POST" >
													{{ csrf_field() }}
													<div class="form-group has-feedback has-feedback-left">
														<input type="text" class="form-control" maxlength="250" name="value" placeholder="Enter comment text" required>
														<div class="form-control-feedback">
															<i style="padding: 10px" class="icon-pencil text-muted"></i>
														</div>
													</div>
				
													<div class="form-group">
														<button type="submit" class="btn btn-primary btn-block">Submit <i class="icon-circle-right2 position-right"></i></button>
													</div>
												</form>

											</div>
										</div>
									</div>
									@endif
								</div>
								<!-- Add Comments -->

								<!-- List Comments -->
								<div class="panel panel-primary">

									<div class="panel-heading">
										<h6 class="panel-title text-semibold">
											<i class="icon-list-unordered position-left"></i> Comment List
										</h6>
									</div>

									{{-- <a href="#">
										<img src="/assets/images/demo/flat/18.png" class="img-responsive" alt="">
									</a> --}}

									<div class="panel-body">
										<ul class="list-feed" style="overflow: hidden;overflow-y:scroll;max-height: 500px;">

											@forelse($orderComments->sortByDesc('id') as $key => $value)
												<li class=" {{ $value->status == 'Success' ? 'border-success' : 'border-danger'  }}">
													<span class="text-muted">{{ $value->created_at }}</span>
													<div class="text-size-small text-primary mb-5">{{ $value->key }} <span style="color: black">by</span> <b>{{ $value->user['full_name'] }}</b></div>
													@if ($value->status != 'Success' && strlen($value->value) > 100)
														{!! strip_tags($value->value) !!} 
													@else
														@if (strpos($value->value, 'html') !== false)
															{{ $value->value }} 
														@else
															{!! $value->value !!} 
														@endif
													@endif
												</li>
											@empty
												<li class="border-danger">
													No Comments 
												</li>
											@endforelse

										</ul>
									</div>
								</div>
								<!-- List Comments -->
							</div>

							<div class="col-sm-2"></div>

							<div class="col-sm-5">
								<div class="content-group">
									<h4><b>Amount: </b></h4>
									<div class="table-responsive no-border">
										<table class="table">
											<tbody>
												<tr>
													<th><b>Subtotal:</b></th>
													<td class="text-right">{{ $currency }} @formatFloat($order->items->where('status','!=',config('enum.item_status')['CANCELLED'])->sum('sub_total'))</td>
												</tr>
												<tr>
													<th><b>Tax: </b></th>
													<td class="text-right">{{ $currency }} @formatFloat($order->items->where('status','!=',config('enum.item_status')['CANCELLED'])->sum('tax'))</td>
												</tr>
												<tr>
													<th><b>Shipping: </b></th>
													<td class="text-right">{{ $currency }} @formatFloat($order->shipping_fee)</td>
												</tr>
												<tr>
													<th><b>Discount{{(isset($order->voucher) && $order->voucher != null ? " (".$order->voucher.") " : "")}}: </b></th>
													<td class="text-right">{{ $currency }} @formatFloat($order->discount)</td>
												</tr>
												@if ($fbr && $order->fbr_tax > 0)
												<tr>
													<th><b>FBR Service Charges: </b></th>
													<td class="text-right">{{ $currency }} @formatFloat($order->fbr_tax)</td>
												</tr>
												@endif

												<tr class="bg-indigo-400">
													<th><b>Grand Total:</b></th>
													<td class="text-right"><h4><b>{{ $currency }} @formatFloat($order->grand_total)</b></h4></td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>

					</div>
				</div>
				<!-- /invoice template -->
			</div>
		</div>

	@include('seller.order.book')

	<script>
		var currency = '{!! $order->currency !!}'+' ';
		var authId = 
			@if(session()->has('user'))
				{!! json_encode(session('user')->id ?? null) !!}
			@else
				{!! Auth::user()->id !!}
			@endif
		;

		$.ajaxSetup({
			headers: {
				'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
			}
		});

		$(document).ready(function(){

			if(@json($create_and_book) == 1){
				$('.book_shipment_button').click();
			}
		});

		$('.print_invoice').bind('click', function() {
			var id  = $(this).data("id");
			
			$.ajax({
				url: '{!! url('seller/order/print_invoice') !!}',
                type: 'post',
				data: {
                    "_token": "{{ csrf_token() }}",
					id:id
				}
			})
			.done(function(data) {
				var win = window.open('', '_blank');
				
				if (win) {
					data.forEach(function(element,index){
						
						$(win.document.body).html(element);
						if (data.length-1 != index ) {
							win = window.open('', '_blank');
						}
						
					});
					
					win.focus();
					win.print();
					
				} else {
					alert('Please allow popups for Unity');
				}
			});
		});


		var id;


		$('.cancelledOrder').on('click', function() {
			id = $(this).data("id");
			$('#modal_cancel_confirm').modal('show');
			$('#order_id').val(id);
		});

		$('#shipment_submit').on('click', function() {
			$('#shipment_submit').attr('disabled', 'disabled');
			$('#createShipment').submit();
		});

		$('.roboCall').on('click', function() {
			var orderId = $(this).data("id");
			$.ajax({
				type: "POST",
				url: '{{ url('/api/robocall/') }}',
				data: {ids: [orderId], auth_id: authId },
				success: function(msg) {
					alert(msg);
				}
			});                     
		});

    </script>

     

@endsection
