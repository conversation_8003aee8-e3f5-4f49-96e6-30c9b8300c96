<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Controllers\API\FFCController;
use App\Http\Controllers\CourierController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\SellerFfcInventoryController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\ShipmentMethod;
use App\Http\Controllers\ShipmentMethodController;
use Illuminate\Support\Facades\Input;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Lead;
use App\Models\Seller;
use App\Models\SellerShipmentMethod;
use App\Models\Setting;
use App\Repository\ShipmentMasterReport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;

Route::post('/grid-data', function () {
    GridEncoder::encodeRequestedData(new ShipmentMasterReport(), Input::all());
});

Route::get('test_shopify', function () {
    $shopify = new App\Models\ShopifyApp();
    $shopify->fullfill_order('1', '1088', 'trax', '12349080', 'http://trax.pk', false);
});

Route::get('/', function () {
	if (url('/') == 'https://app.unityretail.com') {
		return redirect('login');
	}
	return view('index');
});

Route::get('/barcode/generate/{barcode}', 'TestController@generateBarcode');

Route::get('/privacy', function () {
    return view('privacy');
});

Route::get('/faqs', function () {
    return view('faqs');
});

// Route::get('/contact-us', function(){

// 	return view ('contact-us');
// });

Route::post('/contactus_message', function(Request $request){

	$request->validate(

		[
			'recaptcha'  => 'required|recaptcha',
		],
		[
			'recaptcha.required'  => 'Please Make Sure that you are a human',
		]

	);
	try{
		$contact_message = $request;
		// return $contact_message;
		Mail::to('<EMAIL>')->send(new App\Mail\ContactUs($contact_message));

		return true;
	} catch(Exception $e){
		return false;
	}

});

Route::group(['prefix' => 'website'], function () {
    Route::post('seller/register', function (Request $request) {
        $lead = Lead::create(['lead_name' => $request->seller_name, 'brand' => $request->seller_brand, 'email' => $request->seller_email, 'phone' => $request->seller_contact, 'seller_type' => $request->seller_type, 'seller_online' => ($request->sold == 'on') ? true : false, 'lead_type' => 'Seller']);
        Mail::to(env('LEAD_EMAIL'))->send(new App\Mail\LeadReceived($lead));
    });

    Route::post('marketplace/register', function (Request $request) {
        $lead = Lead::create(['lead_name' => $request->name, 'lead_designation' => $request->designation, 'company' => $request->company, 'website' => $request->website, 'email' => $request->email, 'phone' => $request->number, 'lead_type' => 'Marketplace']);
        Mail::to(env('LEAD_EMAIL'))->send(new App\Mail\LeadReceived($lead));
    });
});

// Tracking Service Routes
Route::get('/tracking', 'TrackarController@show')->middleware(CheckTrackarSeller::class);

// Route::get('/tracking/tracking-details/','TrackingController@tracking_details');
Route::get('/tracking/tracking-details', 'TrackingController@tracking_details_ajax');
Route::get('/tracking/tracking-details/{id}', 'TrackingController@tracking_details_link');
Route::post('/tracking/feedback', 'TrackingController@save_feedback');

//want to submit it through form

Route::get('sell/{pid}', 'OrderFormController@show');
Route::post('sell/{pid}', 'OrderFormController@create');

Route::get('public/order/{seller_company_name}', 'OrderFormController@public_order_show');
Route::post('public/order', 'OrderFormController@public_order_create');

Route::get('admin', function () {
    return view('admin_template');
});

// Route::get('playground', 'OrderController@playground');

// Route::get('test', 'TestController@emailSend');
//Route::match(['get', 'post'], '/grid_data', "TestController@data");

// Route::get('grid_data', 'TestController@data');

// Route::post('grid_data', 'TestController@dataChange');

//Route::resource('lead','LeadsController');

// Aramex Cities
Route::get('aramex/{country_code}',function(){
	
});


Route::get('products/images/product/{filename}', function ($filename) {
    $path = storage_path().'/app/product/'.$filename;
    // dd($path);

    if (! File::exists($path)) {
        abort(404);
    }

    $file = File::get($path);
    $type = File::mimeType($path);

    $response = Response::make($file, 200);
    $response->header('Content-Type', $type);

    return $response;
});

Route::get('products/images/{id}', function ($id) {
    $image = App\Models\ProductImage::where('product_id', $id)->first();
    if ($image) {
        $filename = $image->image_url;
    } else {
        $filename = 'product/no_image.png';
    }
    //no_image.png

    $path = storage_path().'/app/'.$filename;
    // dd($path);

    if (! File::exists($path)) {
        abort(404);
    }

    $file = File::get($path);
    $type = File::mimeType($path);

    $response = Response::make($file, 200);
    $response->header('Content-Type', $type);

    return $response;
});

Route::post('get-package-id','Auth\RegisterController@getPackageId');
// Route::get('register/pkg/{id}', 'Auth\RegisterController@showRegistrationForm')->name('register');
// Route::get('register/{id}', 'Auth\RegisterController@showRegistrationForm')->name('register');

Route::get('seller-onboarding', 'Auth\RegisterController@showRegistrationForm')->name('seller-onboarding');
Route::get('seller-onboarding/pkg/{id}', 'Auth\RegisterController@showRegistrationForm');
Route::get('seller-onboarding/{id}', 'Auth\RegisterController@showRegistrationForm');
Route::post('seller-onboarding','Auth\RegisterController@register');

Auth::routes(['register' => false]);

// registration activation routes
Route::get('activation/key/{activation_key}', ['as' => 'activation_key', 'uses' => 'Auth\ActivationKeyController@activateKey']);
Route::get('activation/resend', ['as' =>  'activation_key_resend', 'uses' => 'Auth\ActivationKeyController@showKeyResendForm']);
Route::post('activation/resend', ['as' =>  'activation_key_resend.post', 'uses' => 'Auth\ActivationKeyController@resendKey']);

Route::get('seller/activation/{activation_key}','Auth\ActivationKeyController@sellerActivate')->where('activation_key', '(.*)');

Route::get('/email-activation/{email}',function($enc){
	// return $enc;
	// return view('thanks');
	$enc = base64_decode($enc);
	$d_encod  = urldecode($enc);
	$enc_data= explode('|',$d_encod)[0];
	$enc_iv = explode('|',$d_encod)[1];
	$b_dec = base64_decode($enc_data);
	$email = openssl_decrypt($b_dec,'AES-256-CBC','yoitsasecretkey',0,base64_decode($enc_iv));
	// dd($email);
	$seller = Seller::where('email',$email)->first();
	if(isset($seller)){
		if($seller->email_verified == 1){
			abort(404);
		} else{
			$seller->email_verified = 1;
			$seller->save();

			return view('thanks');
		}
		
	} else{
		abort(404);
	}
});


Route::group(['prefix' => 'seller', 'middleware' => ['auth','sellerSpecific','sellerUser']], function()	{

	Route::post('get-credits',function(){
		return auth()->user()->wallet;
	});

	Route::post('get-free-shipments','ShipmentController@getFreeShipments');

	Route::post('ajax_universal_request','CourierController@ajaxUniversalRequest');

	Route::post('resend-otp', 'SellerDashboardController@resendOtp');
	Route::post('add-credits','PaymentController@addCredits');
	Route::post('pending-credits','PaymentController@addPending');
	Route::get('get-started', 'SellerDashboardController@getStarted');
	Route::get('dashboard', 'SellerDashboardController@index');
	Route::get('dashboard/topup', 'SellerDashboardController@openTopUpWindow');
	Route::post('verify_num', 'SellerDashboardController@phoneNumber');
	Route::get('verification', 'SellerDashboardController@verifyMobile');
	
	Route::post('verifycode', 'SellerDashboardController@verifyCode');
	Route::post('brand-name', 'SellerDashboardController@brandName');
	Route::post('first_order', 'SellerDashboardController@firstOrder');

	
	
	Route::get('details', 'SellerDashboardController@sellerDetails');
	Route::post('storedetails', 'SellerDashboardController@storeDetails');
	Route::post('update-help-steps-status', 'SellerDashboardController@updateHelpStepsStatus');

	
	Route::get('dashboard/shipmentAging/{status}', 'SellerDashboardController@shipmentAging');

	Route::get('dashboard/analytics', 'SellerDashboardController@analytics');
	Route::get('dashboard/orderProductivity', 'SellerDashboardController@orderProductivity');
	Route::get('dashboard/monthOrders', 'SellerDashboardController@monthOrders');
	Route::get('dashboard/dailyFinancials', 'SellerDashboardController@dailyFinancials');

	Route::get('dashboard/customerInsight', 'SellerDashboardController@customerInsight');
	Route::post('dashboard/customerInsight/param', 'SellerDashboardController@customerInsightParam');

	Route::get('dashboard/orderShipmentPieChart', 'SellerDashboardController@orderShipmentPieChart');
	Route::post('dashboard/orderShipmentPieChart/param', 'SellerDashboardController@orderShipmentPieChartParam');

	Route::get('dashboard/topCities', 'SellerDashboardController@topCities');
	Route::post('dashboard/topCities/param', 'SellerDashboardController@topCitiesParam');

	Route::get('dashboard/shipmentAging', 'SellerDashboardController@shipmentAging');
	Route::post('dashboard/shipmentAging/param', 'SellerDashboardController@shipmentAgingParam');

	Route::get('dashboard/paymentAging', 'SellerDashboardController@paymentAging');
	Route::post('dashboard/paymentAging/param', 'SellerDashboardController@paymentAgingParam');

	Route::get('dashboard/shipmentRatio', 'SellerDashboardController@shipmentRatio');
	Route::post('dashboard/shipmentRatio/param', 'SellerDashboardController@shipmentRatioParam');

	Route::get('dashboard/courierRatio', 'SellerDashboardController@courierRatio');
	Route::post('dashboard/courierRatio/param', 'SellerDashboardController@courierRatioParam');

	Route::get('dashboard/courierTotalShipment', 'SellerDashboardController@courierTotalShipment');
	Route::post('dashboard/courierTotalShipment/param', 'SellerDashboardController@courierTotalShipmentParam');

	Route::get('dashboard/dataCount', 'SellerDashboardController@dataCount');
	Route::post('dashboard/dataCount/param', 'SellerDashboardController@dataCountParam');

	Route::get('dashboard/deliveryDays', 'SellerDashboardController@deliveryDays');
	Route::post('dashboard/deliveryDays/param', 'SellerDashboardController@deliveryDaysParam');

	Route::get('cities/search', 'CityController@search');
	Route::get('products/stock-order/search', 'ProductController@searchInStockOrder');
	Route::get('products/order/search', 'ProductController@searchInOrder');
	Route::get('seller-ffc-locations/search', 'SellerFfcLocationController@searchSellerFFCLocation');

	Route::get('header-search', 'SearchController@headerSearch');

	//PRODUCT ROUTES
	Route::get('product', 'ProductController@index');
	Route::get('product/list', 'ProductController@list');
	Route::get('product/grid', 'ProductController@grid');
	Route::get('product/all', 'ProductController@all');
	Route::post('product/grid', function()
	{
    	GridEncoder::encodeRequestedData(new App\Models\ProductsRepository(), Input::all());
	});
	Route::post('product/store', 'ProductController@store');
	Route::post('product/delete', 'ProductController@destroy');
	Route::get('product/create', 'ProductController@create');
	Route::get('product/view/{id}', 'ProductController@show');
	Route::get('product/bulkupload', 'ProductController@bulkupload');
	Route::get('product/images', 'ProductController@images');
	Route::get('product/images/delete/{id}', 'ProductController@imagesDestroy');

	Route::get('product/bulk_add', 'ProductController@bulk_add_view');
	Route::post('product/bulk_add', 'ProductController@bulk_add_save');

	Route::post('product/master-catalogue', 'ProductController@masterCatalogueSave');
	Route::get('product/master-catalogue', 'ProductController@masterCatalogueView');
	Route::get('product/master-catalogue-template', 'ProductController@masterCatalogueTemplate');
	Route::get('product/master-catalogue/sync-products', 'ProductController@syncProducts');
    
	Route::get('product/conflicts', 'ProductConflictController@index');
	Route::get('product/conflicts/all', 'ProductConflictController@all');
	Route::get('product/conflicts/update/{id}', 'ProductConflictController@update');
	Route::get('product/conflicts/ignore/{id}', 'ProductConflictController@ignore');

    Route::post('order/grid', function()
	{
    	GridEncoder::encodeRequestedData(new App\Models\OrderRepository(), Input::all());
	});



	//INVENTORY ROUTES
	Route::get('inventory/seller_locations/set_as_default/{seller_location}', 'SellerLocationController@setAsDefault');
	Route::resource('inventory/seller_locations','SellerLocationController');
	Route::post('inventory/transfer', 'InventoryController@transferStore');
	Route::get('inventory/transfer', 'InventoryController@transferShow');
	Route::post('inventory/transfer/getStockTransferData', 'InventoryController@getStockTransferData');
	Route::post('location/grid', function()
	{
    	GridEncoder::encodeRequestedData(new App\Models\SellerLocationsRepository(), Input::all());
	});


	Route::get('inventory/bulk_add', 'InventoryController@bulk_add_view');
	Route::post('inventory/bulk_add', 'InventoryController@bulk_add_save');
	Route::post('inventory/stock_in_strategy_update', 'InventoryController@stockInStrategyUpdate');

	

	Route::get('inventory/all', 'InventoryController@all');
	Route::get('inventory/committed-stock-details/{id}', 'InventoryController@getCommittedStockDetails');
	Route::resource('inventory','InventoryController');
	Route::post('inventory', 'InventoryController@inventoryReportShow')->name('inventory_ledger_grid');

	Route::get('inventory/location', 'SellerLocationController@index');
	Route::get('inventory/location/storefront-sync', 'SellerLocationController@storefrontSync');
	Route::post('inventory/location/storefront-save', 'SellerLocationController@storefrontSave');
	Route::get('inventory/location/{$location}', 'SellerLocationController@show');
	Route::get('inventory/stock/intake', 'InventoryController@update');
	Route::get('inventory/stock/transfer', 'InventoryController@stock_transfer');

	Route::post('popup_pickup_location', 'SellerLocationController@popupPickupLocation');

	//CONTRACT ROUTES
	Route::resource('contract/marketplaces','MarketplaceController');
	Route::resource('contracts','ContractController');

    // Route::get('contract', 'ContractController@index');
    // Route::get('contract/existing', 'ContractController@show');
    // Route::get('contract/available', 'ContractController@showAvailable');
    // Route::get('contract/create', 'ContractController@create');
    


	Route::resource('user','SellerUserController')->middleware('chk.sub_users');

	//LOADSHEET ROUTE
	Route::get('loadsheet/all_log', 'LoadsheetController@all_log');	
	Route::get('loadsheet/log_entries/{id}', 'LoadsheetController@log_entries');
	Route::post('loadsheet/update_rider', 'LoadsheetController@update');
	Route::post('loadsheet/courier_count', 'LoadsheetController@courierCount');
	Route::post('loadsheet/courier_count_extended', 'LoadsheetController@courierCountExtended');

	
	Route::get('loadsheet/generate_again/{id}', 'LoadsheetController@generateAgain');
	Route::resource('loadsheet', 'LoadsheetController');
	
	
	//STOCK ORDER ROUTES
	Route::post('stock-order/import-product', 'StockOrderController@importProduct');
	Route::get('stock-order/all', 'StockOrderController@all');
	Route::get('stock-order/cancel/{id}', 'StockOrderController@cancel');
	Route::get('stock-order/check-reference/{reference_id}', 'StockOrderController@checkReferenceExists');
	Route::resource('stock-order', 'StockOrderController')->middleware('chk.ffc');
	Route::post('stock-transfer-order/import-product', 'StockOrderController@transferOrderImportProduct');
	Route::get('stock-transfer-order/all', 'StockOrderController@allStockTransferOrder');
	Route::get('stock-transfer-order/check-reference/{reference_id}', 'StockOrderController@stockTransferOrderCheckReferenceExists');
	Route::get('stock-transfer-order/create', 'StockOrderController@stockTransferOrder');
	Route::post('stock-transfer-order/create', 'StockOrderController@stockTransferOrderCreate');
	Route::post('stock-transfer-order/check-product-available', 'StockOrderController@transferOrderCheckProductAvailable');
	Route::post('stock-transfer-order/check-product-available-multiple', 'StockOrderController@transferOrderCheckProductAvailableMultiple');
	Route::get('stock-transfer-order/location-available-product/{location_id}', 'StockOrderController@transferOrderLocationAvailableProduct');
	Route::get('stock-transfer-order/{stock_transfer_order}', 'StockOrderController@stockTransferOrderShow');
	Route::get('stock-transfer-order/cancel/{id}', 'StockOrderController@stockTransferOrderCancel');
	Route::get('stock-transfer-order/gtech-transfer-order-sync/{id}', 'StockOrderController@gtechTransferOrderSync');


	//LOCATION HIERARCHY ROUTES
	Route::resource('location-hierarchy', 'SellerFfcLocationController')->middleware('chk.ffc');
	Route::resource('location-zones', 'ZoneController')->middleware('chk.ffc');
	
	
	//FFC INVENTORY ROUTES
	Route::get('inventory-hierarchy/committed-qty-details/{id}', 'SellerFfcInventoryController@getCommittedQtyDetails');
	Route::resource('inventory-hierarchy', 'SellerFfcInventoryController')->middleware('chk.ffc');
	Route::get('ffc-inventory/all/{location_id}', 'SellerFfcInventoryController@all');
	Route::post('inventory-hierarchy', 'SellerFfcInventoryController@inventoryReportShow')->name('inventory_report_grid');

	//FFC Dashboard ROUTES
	Route::get('ffc-dashboard', 'SellerFfcLocationController@dashboard')->middleware('chk.ffc');
	Route::get('lowstock', 'SellerFfcLocationController@lowStockReport')->middleware('chk.ffc');
	Route::post('lowstock/show', 'SellerFfcLocationController@lowStockReportShow')->name('low_stock_report_grid');
	Route::get('pending-putaway', 'SellerFfcLocationController@pendingPutawayReport')->middleware('chk.ffc');
	Route::post('pending-putaway', 'SellerFfcLocationController@pendingPutawayShow')->name('pending_putaway_report_grid');
	Route::get('pending-putaway/{location_id}', 'SellerFfcLocationController@pendingPutawayDatatable');
	Route::get('replenishment', 'SellerFfcLocationController@replenishmentReport')->middleware('chk.ffc');
	Route::post('replenishment', 'SellerFfcLocationController@replenishmentReportShow')->name('replenishment_report_grid');
	Route::get('replenishment/{location_id}', 'SellerFfcLocationController@replenishmentReportDatatable');
	Route::get('inventory-snaphot', 'SellerFfcLocationController@inventorySnapshotReport')->middleware('chk.ffc');
	Route::post('inventory-snaphot/show', 'SellerFfcLocationController@inventorySnapshotReportShow');
	
	Route::get('stock-out', 'SellerFfcLocationController@stockOutReport')->middleware('chk.ffc');
	Route::post('stock-out', 'SellerFfcLocationController@stockOutShow')->name('stock_out_report_grid');
	Route::get('stock-out/{location_id}', 'SellerFfcLocationController@stockOutDatatable');

	Route::get('ffc-reports', 'SellerFfcLocationController@reports')->middleware('chk.ffc');
	Route::get('report-fulfillment-order', 'SellerFfcLocationController@reportFulfillmentOrder')->middleware('chk.ffc');
	Route::post('report-fulfillment-order/show', 'SellerFfcLocationController@reportFulfillmentOrderShow')->name('report_fulfillment_order_grid');
	Route::get('report-inventory-in-hand', 'SellerFfcLocationController@reportInventoryInHand')->middleware('chk.ffc');
	Route::get('upload-initial-stock-view', 'SellerFfcInventoryController@uploadInitialStockView')->middleware('chk.ffc');
	Route::post('upload-initial-stock-save', 'SellerFfcInventoryController@uploadInitialStockSave')->middleware('chk.ffc');
	Route::get('bin-wise-fulfillment-order-items', 'SellerFfcLocationController@BinWiseFulfillmentOrderItems')->middleware('chk.ffc');
	Route::get('transfer-outbound-dump', 'SellerFfcLocationController@transferOutbountDumpReport')->middleware('chk.ffc');
	Route::post('transfer-outbound-dump/show', 'SellerFfcLocationController@transferOutbountDumpReportShow')->name('report_transfer-outbound-dump_grid');
	Route::get('fulfillment-order-dump', 'SellerFfcLocationController@fulfillmentOrderDumpReport')->middleware('chk.ffc');
	Route::get('fulfillment-order-dump/all/{id}', 'SellerFfcLocationController@fulfillmentOrderDumpReportAll')->middleware('chk.ffc');
	Route::post('fulfillment-order-dump/show', 'SellerFfcLocationController@fulfillmentOrderDumpReportShow')->name('report_fulfillment-order-dump_grid');
	Route::get('inbound-snapshot', 'SellerFfcLocationController@inboundSnapshotReport')->middleware('chk.ffc');
	Route::post('inbound-snapshot/show', 'SellerFfcLocationController@inboundSnapshotReportShow')->name('report_inbound_snapshot');
	Route::get('inbound-receiving', 'SellerFfcLocationController@inboundReceivingReportShow')->middleware('chk.ffc');
	Route::get('inventory-not-available', 'SellerFfcLocationController@inventoryNotAvailableReport')->middleware('chk.ffc');
	Route::post('inventory-not-available/show', 'SellerFfcLocationController@inventoryNotAvailableReportShow')->name('report_inventory_not_available_grid');
	Route::get('packer-performance', 'SellerFfcLocationController@packerPerformanceReport');
	
	
	
	// FFC Packing Desk Routes
	Route::group([
		'middleware' => 'chk.ffc',
		'prefix' => 'pick-pack',
	],function($router){
		$router->get('active-pickers',[SellerFfcInventoryController::class,'pickPackHome']);
		$router->post('release-picklist',[SellerFfcInventoryController::class,'releasePicklist']);
		$router->post('initiate-pack',[SellerFfcInventoryController::class,'initiatePack']);
		$router->post('packing-process',[SellerFfcInventoryController::class,'packingProcess']);
		$router->post('print-awb',[SellerFfcInventoryController::class,'printAWB']);
		$router->post('create-shipment',[SellerFfcInventoryController::class,'createShipment']);
		$router->post('print-invoice',[SellerFfcInventoryController::class,'printInvoice']);
		$router->post('assign-cn',[SellerFfcInventoryController::class,'assignCNAndCreateShipment']);
		$router->post('order-against-barcode',[SellerFfcInventoryController::class,'orderAgainstBarcode']);

		$router->post('release-picklist-order',[SellerFfcInventoryController::class,'releasePicklistOrder']);
		$router->post('submitted-to-pd',[SellerFfcInventoryController::class,'submittedToPd']);

		Route::group([ 'prefix' => 'transfer-order'],function($router){
			$router->get('active-pickers',[SellerFfcInventoryController::class,'pickPackHome']);
			$router->post('release-picklist',[SellerFfcInventoryController::class,'releasePicklistTransferOrder']);
			$router->post('initiate-pack',[SellerFfcInventoryController::class,'initiatePackTransferOrder']);
			$router->post('packing-process',[SellerFfcInventoryController::class,'packingProcessTransferOrder']);
			$router->post('print-awb',[SellerFfcInventoryController::class,'printAWBTransferOrder']);
			$router->post('release-picklist-order',[SellerFfcInventoryController::class,'releasePicklistOrder']);
			$router->post('submitted-to-pd',[SellerFfcInventoryController::class,'submittedToPdTransferOrder']);
		});
	});

	//ORDER ROUTES
	Route::get('order/{id}/passthrough', 'OrderController@passthrough');
	Route::get('order/{id}/fbr-request', 'OrderController@fbrRequest');
	Route::get('order/{id}/resend-to-dom', 'OrderController@reSendToDOM');
	Route::post('order/{id}/add_comment', 'OrderController@addComment');
	Route::post('order/cancelItems', 'OrderController@cancelItems');
	Route::get('order/all', ['as' => 'order.all', 'uses' => 'OrderController@all']);
	Route::get('order/all_main', ['as' => 'order.all_main', 'uses' => 'OrderController@all_main']);
	Route::get('order/all_pending', ['as' => 'order.all_pending', 'uses' => 'OrderController@all_pending']);
	Route::get('order/all_fulfilled', ['as' => 'order.all_fulfilled', 'uses' => 'OrderController@all_fulfilled']);
	Route::get('order/all_cancelled', ['as' => 'order.all_cancelled', 'uses' => 'OrderController@all_cancelled']);
	Route::post('order/print_invoice', ['as' => 'order.print_invoice', 'uses' => 'OrderController@print_invoice']);
	Route::get('order/bulk_cancellation', ['as' => 'order.bulk_cancellation', 'uses' => 'OrderController@bulkCancellation']);
	Route::post('order/getQuote', 'OrderController@getQuote');
	Route::post('order/cancelledOrder', 'OrderController@cancelledOrder');
	Route::get('order/dispatch/{id}/{manual}', 'OrderController@dispatch');
	Route::get('order/rider_areas/{id}', 'OrderController@rider_areas');
	Route::get('order/dispatchCourierService/{id}', 'OrderController@dispatchCourierService');
	Route::get('hold-for-processing-orders', 'OrderController@holdForProcessingOrders');
	Route::get('hold-for-processing-datatable', ['as' => 'order.holdForProcessingDatatable', 'uses' => 'OrderController@holdForProcessingDatatable']);
	Route::get('hold-for-processing-get-order-items', ['as' => 'hold-for-processing.get-order-items', 'uses' => 'OrderController@holdForProcessingOrderItems']);
	Route::post('order/asignLocationToOrderItems', 'OrderController@asignLocationToOrderItems');
	Route::post('order/HFP/reSendToDOM', 'OrderController@reSendToDOMUsingHFP');
	Route::post('order/HFP/assignToSingleLocation', 'OrderController@assignToSingleLocation');
	Route::get('hold-for-processing-get-order-address-areas', 'OrderController@holdForProcessingGetOrderAddressAreas');
	Route::post('hold-for-processing-update-order-address', 'OrderController@holdForProcessingUpdateOrderAddress');

	Route::post('order/sms-trigger', 'OrderController@smsTrigger');
	
	Route::get('export-order', 'OrderController@exportOrder');
	Route::get('order/tag/{order_id}/{tag}', 'OrderController@tagOrder');
	Route::get('order/tagRemove/{order_id}', 'OrderController@tagRemoveOrder');
	Route::post('order/tagAdd/multiple', 'OrderController@tagAddMultiple');
	Route::post('order/tagRemove/multiple', 'OrderController@tagRemoveMultiple');
	Route::post('order/reSendToDOM/multiple', 'OrderController@reSendToDOMMultiple');

	Route::post('order/changeCity/multiple', 'OrderController@changeCityMultiple');
	Route::post('order/changeLocation/multiple', 'OrderController@changeLocationMultiple');
	Route::get('order/storefront-cancellation/{ids}', 'OrderController@storefrontCancellation');
	Route::resource('order', 'OrderController');

	Route::get('reports/cod_receivables', ['as' => 'seller.reports.cod_receivables_view', 'uses' => 'ReportsController@cod_receivables_view']);
	Route::get('reports/cod_receivables/show/{id}', ['as' => 'seller.reports.cod_receivables_show', 'uses' => 'ReportsController@cod_receivables_show']);
	Route::post('reports/cod_receivables/save', ['as' => 'seller.reports.cod_receivables_save', 'uses' => 'ReportsController@cod_receivables_save']);
	Route::get('reports/cod_receivables/show', 'ReportsController@cod_receivables_show_all');
	Route::get('reports/cod_received', 'ReportsController@cod_received_view');
	Route::get('reports/cod_recieved_all', 'ReportsController@cod_recieved_all');

	Route::middleware(['chk.basic_reports'])->group(function () {
		Route::get('reports/ledger-inventory-report', 'ReportsController@ledgerInventoryReportShow');
		Route::get('reports/ledger-inventory-report-all', 'ReportsController@ledgerInventoryReportAll');
		Route::get('reports/ffc-inventory-report', 'ReportsController@ffcInventoryReportShow');
		Route::get('reports/ffc-inventory-report-all', 'ReportsController@ffcInventoryReportAll');
		Route::get('reports/cod-liability-report', 'ReportsController@codLiabilityReportShow');
		Route::get('reports/cod-liability-report/{drill_down_type}/{courier_id}', 'ReportsController@codLiabilityReportDrillDown');
		Route::get('reports/cod-liability-report-all/{drill_down_type}/{courier_id}', 'ReportsController@codLiabilityReportDrillDownAll');
		Route::get('reports/cod-receivables-aging-report', 'ReportsController@codReceivablesAgingReportShow');
		Route::post('reports/cod-receivables-aging-report', 'ReportsController@codReceivablesAgingReportShow');
		Route::get('reports/cod-receivables-aging-report/{drill_down_type}/{courier_id}/{date_range?}', 'ReportsController@codReceivablesAgingReportDrillDown');
		Route::get('reports/cod-receivables-aging-report-all/{drill_down_type}/{courier_id}/{date_range?}', 'ReportsController@codReceivablesAgingReportDrillDownAll');
		Route::get('reports/cod-return-in-transit-aging-report', 'ReportsController@codReturnInTransitAgingReportShow');
		Route::get('reports/cod-return-in-transit-aging-report/{drill_down_type}/{courier_id}', 'ReportsController@codReturnInTransitAgingReportDrillDown');
		Route::get('reports/cod-return-in-transit-aging-report-all/{drill_down_type}/{courier_id}', 'ReportsController@codReturnInTransitAgingReportDrillDownAll');
		Route::get('reports/cod-return-report', 'ReportsController@codReturnReportShow');
		Route::get('reports/cod-return-report/{location_id}/{courier_id}', 'ReportsController@codReturnReportDrillDown');
		Route::get('reports/cod-return-report-all/{location_id}/{courier_id}', 'ReportsController@codReturnReportDrillDownAll');
		Route::get('reports/ops-productivity-report', 'ReportsController@opsProductivityReportShow');
		Route::get('reports/ops-productivity-report/all', 'ReportsController@opsProductivityReportAll');
		Route::get('reports/ops-productivity-report/init', 'ReportsController@opsProductivityReportInit');
		Route::get('reports/courier-performance-report', 'ReportsController@courierPerformanceReportShow');
		Route::get('reports/courier-performance-report/init', 'ReportsController@courierPerformanceReportInit');
		Route::get('reports/courier-performance-report/all', 'ReportsController@courierPerformanceReportAll');
		Route::post('reports/shipments/show', 'ReportsController@shipmentsShow');
		Route::post('reports/shipments_drilldown', 'ReportsController@shipmentDrilldown');
		Route::get('reports/reports-shipments-all', 'ReportsController@reportsShipmentAll');
		Route::post('reports/orders/show', 'ReportsController@ordersShow');
		Route::get('reports/order-master-report','ReportsController@orderMasterReportShow');
		Route::get('reports/order-master-report/all','ReportsController@orderMasterReportAll');
		Route::post('reports/shipment-master-report/download','ReportsController@shipmentMasterReportDownload');
		Route::get('reports/shipment-master-report','ReportsController@shipmentMasterReportShow');
		Route::get('reports/shipment-master-report/all','ReportsController@shipmentMasterReportAll');
		Route::get('reports/return-receiving-report','ReportsController@returnReceivingReportShow');
		Route::get('reports/return-receiving-report/all','ReportsController@returnReceivingReportAll');
		Route::get('reports/exception-report','ReportsController@exceptionReportShow');
		Route::get('reports/exception-report/all','ReportsController@exceptionReportAll');
	});
	
	Route::post('reports/order-data-dump-report/generate','ReportsController@orderDataDumpReportGen')->name('generate_order_data_dump');
	Route::get('reports/order-data-dump-report','ReportsController@orderDataDumpReport');
	Route::get('reports/order-cancellation-reasons-report','ReportsController@orderCancellationReasonReport');
	Route::get('reports/product-dump-report','ReportsController@productDumpReport');
	Route::get('reports/courier-statuses-history-report','ReportsController@courierStatusesHistoryReport');
	Route::get('reports/shipment-master-dump-report','ReportsController@shipmentMasterDumpReport');
	Route::get('reports/shipment-status-history-report','ReportsController@shipmentStatusHistoryReport');
	Route::get('reports/seven-days-dispatch-dump-report','ReportsController@sevenDaysDispatchDumpReport');	
	Route::get('reports/lost-shipment-items-report','ReportsController@lostShipmentsAndItemsReport');
	Route::get('reports/shipment-status-timestamp-report','ReportsController@shipmentStatusTimestampReport');

	Route::get('reports/cod_payments', ['as' => 'seller.reports.cod_payments_view', 'uses' => 'ReportsController@cod_payments_view']);
	Route::post('reports/cod_payments/store', 'ReportsController@cod_payments_store');
	Route::post('reports/cod_payments/update/{id}', 'ReportsController@cod_payments_update');
	Route::get('reports/cod_payments/show-single-payment-shipment/{id}', 'ReportsController@showSinglePaymentShipments');
	Route::post('reports/cod_payments/confirmation', 'ReportsController@paymentConfirmation');
	Route::get('reports/cod_payments/create', ['as' => 'seller.reports.cod_payments_create', 'uses' => 'ReportsController@cod_payments_create']);
	Route::get('reports/cod_payments/show-payments', 'ReportsController@showPayments');
	Route::get('reports/cod_payments/courier_shipments_create/{id}', 'ReportsController@courierShipmentsCreate');
	Route::get('reports/cod_payments/courier_shipments_update/{courier_id}/{cod_payment_id}', 'ReportsController@courierShipmentsUpdate');
	Route::get('reports/cod_payments/show-single-payment/{id}', 'ReportsController@cod_payments_single_view');

	Route::middleware(['chk.advanced_reports'])->group(function () {
		Route::get('reports/aging-report','ReportsController@agingReportShow');
		Route::get('reports/aging-report-all','ReportsController@agingReportAll');
		Route::prefix('reports')->group(function () {
			Route::get('last-mile-aging-report', 'ReportsController@lastMileAgingReportPage');
			Route::post('last-mile-aging-report/show', 'ReportsController@lastMileAgingReportShow')->name('last_mile_aging_report_in_grid');

			Route::get('aging-report-month-wise','ReportsController@agingReportMonthWiseShow');

			Route::get('aging-report-courier-wise','ReportsController@agingReportCourierWiseShow');

			Route::get('aging-report-day-wise','ReportsController@agingReportDayWiseShow');

			Route::get('aging-report-time-wise','ReportsController@agingReportTimeWiseShow');

			Route::get('order-delivery-tat','ReportsController@orderDeliveryTat');
			Route::post('orderDeliveryTat/show', 'ReportsController@orderDeliveryTatShow')->name('order_delivery_tat_in_grid');

			Route::get('last-mile-payments-reconciliation','ReportsController@lastMilePaymentsReconciliation');
			Route::post('last-mile-payments-reconciliation/show', 'ReportsController@lastMilePaymentsReconciliationShow')->name('last_mile_payments_reconciliation_grid');

			Route::get('last-mile-reconciliation-sheet','ReportsController@lastMileReconciliationSheet');
			Route::post('last-mile-reconciliation-sheet/show', 'ReportsController@lastMileReconciliationSheetShow')->name('last_mile_reconciliation_sheet_grid');
			Route::get('last-mile-reconciliation-sheet/grid', 'ReportsController@lastMileReconciliationSheetGrid');

			Route::get('fulfillment-velocity-report','ReportsController@fulfillmentVelocityReport');
			Route::post('fulfillment-velocity-report/show', 'ReportsController@fulfillmentVelocityReportShow')->name('fulfillment_velocity_report_grid');
			
			Route::get('courier-kpi', 'ReportsController@courierKPIReportShow');
			Route::post('courier-kpi', 'ReportsController@courierKPIReportData');

			Route::get('last-mile-dashboard', 'ReportsController@lastMileDashboardReportShow');
			Route::post('last-mile-dashboard', 'ReportsController@lastMileDashboardReportData');




			Route::get('order-cancellation-analysis-report','ReportsController@orderCancellationAnalysisReport');
			Route::post('order-cancellation-analysis-report/show', 'ReportsController@orderCancellationAnalysisReportShow')->name('order_cancellation_analysis_report');
			Route::get('sales-analysis-report','ReportsController@salesAnalysisReport');
			Route::post('sales-analysis-report/show', 'ReportsController@salesAnalysisReportShow')->name('sales_analysis_report_grid');
			
		});

	});
	



	Route::resource('reports', 'ReportsController');

	// Customer Profiling
	Route::get('customer_profiling','CustomerController@index')->middleware('chk.customer_profiling');
	Route::get('customer_profiling/show', 'CustomerController@show_data');
	Route::get('customer_profiling/ind-summary/{id}', 'CustomerController@ind_summary');
	Route::get('customer_profiling/get_value', 'CustomerController@get_value');
	Route::get('customer_profiling/get_name', 'CustomerController@get_name');
	Route::get('customer_profiling/get_email', 'CustomerController@get_email');
	Route::get('customer_profiling/get_orders/{id}', 'CustomerController@get_orders');
	Route::get('customer_profiling/get_shipments/{id}', 'CustomerController@get_shipments');
	// Route::get('customer_profiling/pending-orders-count', 'CustomerController@pending_orders_count');
	Route::get('customer_profiling/get_badge', 'CustomerController@get_badge');
	// Route::get('customer_profiling/registration_date', 'CustomerController@registration_date');
	// Route::get('customer_profiling/latest_order', 'CustomerController@latest_order');

	Route::get('customer/get-return-ratio/{number}','CustomerController@getReturnRatio');


	// Tracking Service Routes
	Route::post('trackar/intimations','TrackarController@intimationsSave');
	Route::post('trackar/customjs','TrackarController@customJsSave');
	Route::get('trackar','TrackarController@show')->middleware('chk.trackar');
	Route::post('trackar/settings','TrackarController@settings');
	Route::get('trackar/feedback', 'TrackarController@show_feedback');

	// Return Merchandise Authorization
	Route::get('rma/return-requests', 'ReturnMerchandiseAuthorizationController@returnRequests');//
	Route::get('rma/return-request-data/{id}', 'ReturnMerchandiseAuthorizationController@returnRequestData');//
	Route::get('settings/rma-settings', 'ReturnMerchandiseAuthorizationController@rmaSettingsHome')->middleware('chk.rma');//
	Route::post('settings/save-policy-message','ReturnMerchandiseAuthorizationController@savePolicyMessage');//
	Route::post('settings/save-deadline','ReturnMerchandiseAuthorizationController@saveDeadline');//
	Route::post('settings/save-api-secret','ReturnMerchandiseAuthorizationController@saveApiSecret');//
	Route::post('settings/rma-action','ReturnMerchandiseAuthorizationController@saveAction');
	Route::post('rma/save-request','ReturnMerchandiseAuthorizationController@saveRequest');//
	Route::post('rma/decision-revert','ReturnMerchandiseAuthorizationController@decisionRevert');//
	Route::get('rma','ReturnMerchandiseAuthorizationController@index');//
	Route::post('rma/support-conversation','ReturnMerchandiseAuthorizationController@supportConversation');//
	Route::post('rma/submit-message-reply','ReturnMerchandiseAuthorizationController@submitMessageReply');//
	// Route::resource('rma', 'ReturnMerchandiseAuthorizationController');

	Route::get('get-payment-info-option',[CourierController::class,'getPayemntInfoOption']);

	// Reverse Shipment
	Route::get('reverse-shipment/print_address_labels', 'ReverseShipmentController@print_address_labels');
	Route::get('reverse-shipment/print_sticker_labels', 'ReverseShipmentController@print_sticker_labels');
	Route::get('reverse-shipment/all_dispatched', 'ReverseShipmentController@all_dispatched');
	Route::get('reverse-shipment/all_fulfilled', 'ReverseShipmentController@all_fulfilled');
	Route::get('reverse-shipment/all_cancelled', 'ReverseShipmentController@all_cancelled');
	Route::get('reverse-shipment/cancelled/{id}', 'ReverseShipmentController@cancelled');
	Route::get('reverse-shipment/delivered/{id}', 'ReverseShipmentController@delivered');
	Route::resource('reverse-shipment','ReverseShipmentController');

    // Return Received
	Route::get('return-received','ReturnReceivedController@index');
	Route::post('return-received','ReturnReceivedController@getShipments');
	Route::post('return-received/mark','ReturnReceivedController@markShipments');
	Route::get('return-received/bin-against-barcode/{barcode}','ReturnReceivedController@ffcBinAgainstBarcode');
    
    /// Carton Management
    Route::get('cartons/all', 'CartonController@getAll');
    Route::post('cartons/toggle-status', 'CartonController@toggleStatus')->name('cartons.toggle-status');
    Route::resource('cartons', 'CartonController');

	Route::get('settings/shipping', 'SettingController@shipping');
	Route::get('settings/change_password', 'SettingController@changePassword');
	Route::post('settings/shipping', 'SettingController@shippingStore');
	Route::post('settings/change_password', 'SettingController@changePasswordStore');
	Route::get('settings/botsifyintegration', 'SettingController@BotsifyIntegration');
	Route::get('settings/integration', 'SettingController@Integration');
	Route::post('settings/integration', 'SettingController@saveIntegration');
	Route::post('settings/flash', 'SettingController@saveFlash');
	Route::post('settings/marco', 'SettingController@saveMarco');
	Route::post('settings/three-pl', 'SettingController@saveThreePL');
	Route::post('settings/botsify', 'SettingController@saveBotsify');
	Route::post('settings/inventory-update-api-key', 'SettingController@saveInventoryUpdateApiKey');
	Route::get('settings/timezone', 'SettingController@setTimezone');
	Route::post('settings/timezone', 'SettingController@setTimezoneStore');
	Route::post('settings/customOrderEndpoint', 'SettingController@customOrderEndpoint');

	// Audit Log
	Route::get('settings/audit-logs', 'AuditLogController@homeIndex');
	Route::get('settings/audit-logs/show', 'AuditLogController@homeShow');
	Route::resource('settings/audit-log-settings', 'AuditLogController');

	// Billing Profile
	Route::get('settings/billing-profile','BillingController@index');
	Route::put('settings/billing-profile/{id}','BillingController@update');

	// Exception List
	Route::get('settings/exception-list','SettingController@exceptionListAll');
	Route::post('settings/exception-list','SettingController@exceptionListSave');
	Route::delete('settings/exception-list/{id}','SettingController@exceptionListDelete');

	// Transaction History
	Route::get('settings/get-transactions','WalletTransactionController@getTransactions');
	Route::get('settings/transactions','WalletTransactionController@index');

	Route::resource('distributed_system','DistributedController');

	// Route to download storage files
	Route::get('download_files/{topic}','SettingController@downloadStorageFiles')->name('download_files');
	Route::post('generate-pandago-keys','CourierController@generatePandagoKeys');

	Route::resource('settings/auto_shipped','AutoShippedController');
	Route::get('settings/auto_shipped_setting','AutoShippedController@auto_shipped_setting');
	Route::post('settings/auto_shipped_setting','AutoShippedController@auto_shipped_setting_save');
	Route::post('settings/auto_shipped/job_schedule', 'AutoShippedController@job_schedule');
	Route::get('settings/courier_limit','CourierController@courier_limit_view');
	Route::post('settings/courier_limit','CourierController@courier_limit_save');
	Route::post('settings/default_courier','CourierController@default_courier_save');
	Route::resource('settings/tag','TagController');

	// Cancellation Reason
	Route::post('settings/reason/cancel-home','ReasonController@cancelReasonSave');
	Route::get('settings/reason/cancel-home','ReasonController@cancelReasonHome');
	Route::put('settings/reason/cancel-home/{id}','ReasonController@cancelReasonUpdate');
	Route::get('settings/reason/cancel-home/{id}/edit','ReasonController@cancelReasonEdit');
	

	Route::resource('settings/reason','ReasonController');

	Route::get('check-mps',[SettingController::class,'checkMps']);

	

	Route::get('sticker_label','ShipmentController@stickerLabelView');
	Route::post('sticker_label','ShipmentController@stickerLabelGet');

	Route::get('auto_shipped', 'AutoShippedController@autoShippedHome');
	Route::post('auto_shipped/get_review', 'AutoShippedController@getReview');
	Route::get('auto_shipped/all_log', 'AutoShippedController@all_log');
	Route::get('auto_shipped/log_entries/{id}', 'AutoShippedController@log_entries');
	Route::get('auto_shipped/cancel/{shippingLog}', 'AutoShippedController@cancel');
	Route::post('auto_shipped/run', 'AutoShippedController@run');
	//Route::resource('settings', 'SettingController');


	// Fulfillment Order Routes
	Route::get('fulfillment-orders','FulfillmentOrderController@index');
	Route::post('fulfillment-orders', 'FulfillmentOrderController@fulfilmentOrderShow')->name('fulfillment_report_grid');
	Route::get('fulfillment-orders/all-open/{location_id}','FulfillmentOrderController@allOpen');
	Route::post('fulfillment-orders/assign-user','FulfillmentOrderController@assignUser');
	Route::post('fulfillment-orders/reject','FulfillmentOrderController@reject');
	Route::get('fulfillment-orders/get-zones-orders/{location_id}/{zones}','FulfillmentOrderController@getZonesOrders');
	

	// Shipper Advice Routes
	Route::post('shipper-advice/reattempt/decision','ShipperAdviceController@reattemptDecision');
	Route::post('shipper-advice/return/decision','ShipperAdviceController@returnDecision');
	Route::get('shipper-advice','ShipperAdviceController@index');
	Route::get('shipper-advice/all_pending', ['as' => 'shipper-advice.all_pending', 'uses' => 'ShipperAdviceController@all_pending']);
	Route::get('shipper-advice/all_reattempt', ['as' => 'shipper-advice.all_reattempt', 'uses' => 'ShipperAdviceController@all_reattempt']);
	Route::get('shipper-advice/all_return', ['as' => 'shipper-advice.all_return', 'uses' => 'ShipperAdviceController@all_return']);

	Route::get('shipment/all', ['as' => 'shipment.all', 'uses' => 'ShipmentController@all']);
	Route::get('shipment/all_dispatched', ['as' => 'shipment.all_dispatched', 'uses' => 'ShipmentController@all_dispatched']);
	Route::get('shipment/all_fulfilled', ['as' => 'shipment.all_fulfilled', 'uses' => 'ShipmentController@all_fulfilled']);
	Route::get('shipment/all_cancelled', ['as' => 'shipment.all_cancelled', 'uses' => 'ShipmentController@all_cancelled']);
	Route::get('shipment/trax_tracking/{tracking_number}', ['as' => 'shipment.trax_tracking', 'uses' => 'ShipmentController@trax_tracking']);
	Route::get('shipment/del_trax_address_labels', ['as' => 'shipment.del_trax_address_labels', 'uses' => 'ShipmentController@del_trax_address_labels']);
	Route::get('shipment/commercial_invoice', ['as' => 'shipment.commercial_invoice', 'uses' => 'ShipmentController@commercial_invoice']);
	Route::post('shipment/print_address_labels', ['as' => 'shipment.print_address_labels', 'uses' => 'ShipmentController@print_address_labels']);
	Route::post('shipment/print_loadsheets', ['as' => 'shipment.print_loadsheets', 'uses' => 'ShipmentController@print_loadsheets']);
	Route::post('shipment/print_sticker_labels', ['as' => 'shipment.print_sticker_labels', 'uses' => 'ShipmentController@print_sticker_labels']);
	Route::post('shipment/print_sticker_label_on_thermal', ['as' => 'shipment.print_sticker_label_on_thermal', 'uses' => 'ShipmentController@print_sticker_label_on_thermal']);
	Route::post('shipment/print_invoices', ['as' => 'shipment.print_invoices', 'uses' => 'ShipmentController@print_invoices']);
	Route::post('shipment/download_invoices', ['as' => 'shipment.download_invoices', 'uses' => 'ShipmentController@download_invoices']);
	Route::post('shipment/print_thermal_invoices', ['as' => 'shipment.print_thermal_invoices', 'uses' => 'ShipmentController@print_thermal_invoices']);
	Route::post('shipment/print_shipping_invoices', ['as' => 'shipment.print_shipping_invoices', 'uses' => 'ShipmentController@print_shipping_invoices']);
	Route::get('shipment/bulk_cancellation', ['as' => 'shipment.bulk_cancellation', 'uses' => 'ShipmentController@bulk_cancellation']);
	Route::get('shipment/bulk_deliver', ['as' => 'shipment.bulk_deliver', 'uses' => 'ShipmentController@bulk_deliver']);
	Route::get('shipment/bulk_return', ['as' => 'shipment.bulk_return', 'uses' => 'ShipmentController@bulk_return']);
	Route::get('shipment/bulk_gtech_sync', ['as' => 'shipment.bulk_deliver', 'uses' => 'ShipmentController@bulkGtechSync']);
	Route::get('shipment/bulk_erp_jdot_sales_sync', ['as' => 'shipment.bulk_deliver', 'uses' => 'ShipmentController@bulkERPSalesSync']);
	Route::get('shipment/bulk_technosys_sync', ['as' => 'shipment.bulk_deliver', 'uses' => 'ShipmentController@bulkTechnosysSync']);

	Route::resource('shipment', 'ShipmentController');
	Route::get('shipment/delivered/{id}', 'ShipmentController@delivered');
	Route::get('shipment/returned/{id}', 'ShipmentController@returned');
	Route::get('shipment/cancelled/{id}', 'ShipmentController@cancelled');
	Route::get('shipment/force-track/{id}', 'ShipmentController@forceTrack');
	Route::get('shipment/force-status-sync/{shipment}/{id}', 'ShipmentController@forceStatusSync');
	Route::get('shipment/reason/{shipment_id}/{reason}', 'ShipmentController@reasonShipment');
	Route::get('shipment/reasonRemove/{shipment_id}', 'ShipmentController@reasonRemoveShipment');
	Route::get('shipment_csv/', 'ShipmentController@csvShipment');
	Route::get('packaging_sheet','ShipmentController@packaging_sheet');
	Route::get('shipment/storefront-tagging/{ids}', 'ShipmentController@storefrontTagging');
	Route::get('shipment/storefront-status-sync/{ids}', 'ShipmentController@storefrontStatusSync');
	Route::get('shipment/{id}/fbr-request', 'ShipmentController@fbrRequest');
	Route::get('shipment/gtech-sync/{id}', 'ShipmentController@gtechSync');
	Route::get('shipment/gtech-cancel-sync/{id}', 'ShipmentController@gtechCancellationSync');
	Route::get('shipment/erp-jdot-sales-sync/{id}', 'ShipmentController@jdotERPSync');
	Route::get('shipment/erp-jdot-return-sales-sync/{id}', 'ShipmentController@erpJdotReturnSalesSync');

	Route::get('shipment/technosys-sync/{id}', 'ShipmentController@technosysSync');
	
	Route::post('coupon/apply','CouponController@apply');

	Route::get('loadsheet/view', ['as' => 'loadsheet.view', 'uses' => 'ShipmentController@loadsheet_view']);
	Route::get('loadsheet/all', ['as' => 'loadsheet.all', 'uses' => 'ShipmentController@loadsheet_all']);
	Route::get('loadsheet/print', ['as' => 'loadsheet.print', 'uses' => 'ShipmentController@loadsheet_print']);

	Route::get('settings/couriers', ['as' => 'seller_settings_couriers_view', 'uses' => 'CourierController@settings_view']);
	Route::get('settings/couriers/{id}', ['as' => 'seller_settings_courier_view', 'uses' => 'CourierController@settings_courier_view']);
	Route::post('settings/couriers/{id}', ['as' => 'seller_settings_courier_save', 'uses' => 'CourierController@settings_courier_save']);
	Route::get('settings/couriers/enable-universal/{id}', 'CourierController@enableUniversal');
	Route::get('settings/couriers/disable-universal/{id}', 'CourierController@disableUniversal');

	Route::get('settings/auto-order-confirmation', 'SettingController@autoOrderConfirmation');
	Route::post('settings/auto_order_confirmation', 'SettingController@autoOrderConfirmationSave');
	Route::post('settings/auto_order_confirmation_exclusions', 'SettingController@autoOrderConfirmationExclusionSave');
	Route::post('settings/assign_orders_to_location_setting', 'SettingController@assignOrderToLocationSetting');
	Route::post('settings/address_validation_settings', 'SettingController@AddressValidationSetting');

	
	Route::get('settings/get_areas/{id}', 'DefaultPickupAddressController@getAreas');

	Route::resource('settings/shipping_settings', 'SellerShippingSettingsController');

	Route::post('settings/shipping_settings/order-mark-paid', 'SellerShippingSettingsController@orderMarkPaid');

	Route::post('settings/shipping_settings/weight', 'SellerShippingSettingsController@weightStore');
	Route::post('settings/shipping_settings/inventory', 'SellerShippingSettingsController@inventoryStore');
	Route::post('settings/shipping_settings/location', 'SellerShippingSettingsController@locationStore');
	Route::post('settings/shipping_settings/auto_shipped', 'SellerShippingSettingsController@autoShipped');
	Route::post('settings/shipping_settings/auto_shipped_created_order', 'SellerShippingSettingsController@autoShippedCreatedOrder');
	Route::post('settings/shipping_settings/auto_shipped_tagged_order', 'SellerShippingSettingsController@autoShippedTaggedOrder');
	Route::post('settings/shipping_settings/order_fulfilment', 'SellerShippingSettingsController@orderFulfilment');
	Route::post('settings/shipping_settings/two_way_sync', 'SellerShippingSettingsController@twoWaySync');
	Route::post('settings/shipping_settings/order_status_on_lost', 'SellerShippingSettingsController@orderStatusOnLost');
	Route::post('settings/shipping_settings/shipment_detail', 'SellerShippingSettingsController@shipmentDetail');
	Route::post('settings/shipping_settings/shipment_remark', 'SellerShippingSettingsController@shipmentRemark');
	Route::post('settings/shipping_settings/daily-updates', 'SellerShippingSettingsController@dailyUpdates');
	Route::post('settings/shipping_settings/courier_limit', 'SellerShippingSettingsController@courierLimit');
	Route::post('settings/shipping_settings/fulfillment', 'SellerShippingSettingsController@fulfillment');
	Route::post('settings/shipping_settings/fbr-sales-posting', 'SellerShippingSettingsController@fbrSalesPosting');
	Route::post('settings/shipping_settings/auto_robocall', 'SellerShippingSettingsController@autoRobocall');
	Route::post('settings/shipping_settings/sticker_shipment_detail', 'SellerShippingSettingsController@stickerShipmentDetail');
	Route::post('settings/shipping_settings/distributed_system', 'SellerShippingSettingsController@distributedSystem');
	Route::post('settings/shipping_settings/customer_order_confirmation', 'SellerShippingSettingsController@customerOrderConfirmation');
	Route::post('settings/shipping_settings/reverse-courier', 'SellerShippingSettingsController@reverseCourier');
	Route::post('settings/shipping_settings/product-hs-code', 'SellerShippingSettingsController@productHSCode');
	Route::post('settings/shipping_settings/page_break', 'SellerShippingSettingsController@pageBreak');
	Route::post('settings/shipping_settings/omni_location_assignment', 'SellerShippingSettingsController@omniLocationAssignmentStore');
	Route::post('settings/shipping_settings/fetch_barcodes_from_storefront', 'SellerShippingSettingsController@fetchBarcodesFromStoreFront');

	Route::post('settings/shipping_settings/put_away_location', 'SellerShippingSettingsController@putAwayLocation');
	Route::post('settings/shipping_settings/pick_list_batch', 'SellerShippingSettingsController@pickListBatch');
	Route::post('settings/shipping_settings/retail_inventory_buffer', 'SellerShippingSettingsController@retailInventoryBuffer');


	
	Route::post('settings/shipping_settings/auto_shipping_method_booking', 'SellerShippingSettingsController@autoShippingMethodBooking');
	Route::post('settings/shipping_settings/order_config_settings', 'SellerShippingSettingsController@orderConfigSettings');

	Route::get('settings/order_settings', 'SellerOrderSettingsController@index')->name('order_settings');
	Route::post('settings/order_settings/storefront_order_cancellation', 'SellerOrderSettingsController@storefrontOrderCancellation');
	Route::post('settings/order_settings/storefront_restock_items', 'SellerOrderSettingsController@storefrontRestockItems');
	Route::post('settings/order_settings/storefront_notify_customer_on_cancellation', 'SellerOrderSettingsController@storefrontNotifyCustomerOnCancellation');



	/// Shipment Settings
	Route::get('settings/shipment_settings', 'SellerShipmentSettingsController@index')->name('shipment_settings');
	Route::post('settings/shipment_settings/return_receiving', 'SellerShipmentSettingsController@returnReceiving');
	Route::post('settings/shipment_settings/return_receiving_item_type_bins', 'SellerShipmentSettingsController@returnReceivingItemTypeBins');


	Route::get('settings/invoice_customization','SettingController@customInvoiceSettingsShow');
	Route::post('settings/invoice_customization_save','SettingController@customInvoiceSettingsSave');

	Route::get('settings/fbr','SettingController@fbrSettingsShow');
	Route::post('settings/fbr_save','SettingController@fbrSettingsSave');

	Route::resource('settings/shipping_methods', 'ShipmentMethodController')->middleware('chk.shipping_method');
	Route::resource('settings/payment_methods', 'PaymentController')->middleware('chk.payment_method');

	Route::resource('terms_of_service_agreement', 'TermsofServiceAgreementController');

	Route::get('settings/ffc_settings', [SellerFfcInventoryController::class,'showFfcSettings']);
	Route::post('settings/ffc_settings/packing_desk_label_setting', [SellerFfcInventoryController::class,'packingDeskLabelSetting']);
	Route::post('settings/ffc_settings/location_settings_update', [SellerFfcInventoryController::class,'updateSellerLocationSettings']);
	Route::post('settings/ffc_settings/picking_setting', [SellerFfcInventoryController::class,'pickingSetting']);


	Route::post('settings/ffc_settings/product_threshold', [SellerFfcInventoryController::class,'productThresholdlSetting']);
	Route::post('settings/ffc_settings/storage_location_threshold', [SellerFfcInventoryController::class,'storageLocationThresholdSetting']);
	Route::post('settings/ffc_settings/packing_desk_invoice_setting', [SellerFfcInventoryController::class,'packingDeskInvoiceSetting']);
	Route::post('settings/ffc_settings/order_comments_in_packing_desk_setting', [SellerFfcInventoryController::class,'orderCommentsInPackingDeskSetting']);
	Route::post('settings/ffc_settings/over_delivery_settings', [SellerFfcInventoryController::class,'overDeliveryeSetting']);
	Route::post('settings/ffc_settings/item_wise_settings', [SellerFfcInventoryController::class,'itemWiseSetting']);
	Route::post('settings/ffc_settings/manual_cn_assignment', [SellerFfcInventoryController::class,'manualCNAssignmentSetting']);
	Route::post('settings/ffc_settings/item_wise_putaway_settings', [SellerFfcInventoryController::class,'itemWisePutAwaySetting']);
	Route::post('settings/ffc_settings/item_wise_scan_at_picking_settings', [SellerFfcInventoryController::class,'itemWiseScanAtPickingSetting']);
	Route::post('settings/ffc_settings/pick_list_international_order', [SellerFfcInventoryController::class,'pickListInternationalOrderSetting']);
	Route::post('settings/ffc_settings/assign_orders_to_ffc_setting', [SellerFfcInventoryController::class,'assignOrderToFFCSetting']);

	Route::get('settings/omni_location_assignment', [SellerFfcInventoryController::class,'showOmniLocationSettings'])->name('omni_assignment_settings');
	Route::post('settings/omni_location_assignment/order_split_setting', [SellerFfcInventoryController::class,'orderSplitSetting']);
	Route::post('settings/omni_location_assignment/ol_determination_mech', [SellerFfcInventoryController::class,'omniLocationDeterminationMech']);
	Route::post('settings/omni_location_assignment/ol_determination_trig', [SellerFfcInventoryController::class,'omniLocationDeterminationTrig']);
	Route::post('settings/omni_location_assignment/load_balancing_setting', [SellerFfcInventoryController::class,'loadBalancingSetting']);
	Route::get('settings/omni_location_assignment_overide/{id}/edit',[SellerFfcInventoryController::class,'omniLocationAssignmentOverideEdit']);
	Route::post('settings/omni_location_assignment_overide/update',[SellerFfcInventoryController::class,'omniLocationAssignmentOverideUpdate']);
	Route::get('settings/omni_location_assignment_overide/create',[SellerFfcInventoryController::class,'omniLocationAssignmentOverideCreate']);
	Route::post('settings/omni_location_assignment_overide/store',[SellerFfcInventoryController::class,'omniLocationAssignmentOverideStore']);
	Route::post('settings/omni_location_assignment/proximity_based_on', [SellerFfcInventoryController::class,'omniLocationProximityBasedOn']);
	Route::get('settings/omni_location_locations_priority/{id}/edit',[SellerFfcInventoryController::class,'omniLocationPriorityEdit']);
	Route::post('settings/omni_location_locations_priority/update',[SellerFfcInventoryController::class,'omniLocationPriorityUpdate']);
	Route::post('settings/omni_location_assignment/label_format_setting', [SellerFfcInventoryController::class,'omniLocatioLabelFormat']);
	Route::post('settings/omni_location_assignment/geographical_scope_settings', [SellerFfcInventoryController::class,'geographicalScopeSettings']);
	Route::post('settings/omni_location_assignment/geographical_proximity_based_on', [SellerFfcInventoryController::class,'geographicalProximityBasedOn']);

	//Scan and Ship
	Route::get('scan-n-ship','ScanNShipController@index');
	Route::post('scan-n-ship','ScanNShipController@getFOShipment');
	Route::post('scan-n-ship/pack','ScanNShipController@pack');

});

Route::group(['prefix' => 'cron'], function() {
	Route::get('check-confirmed-orders', 'OrderController@checkConfirmOrders');
	Route::get('check-robocall-initiated-orders', 'OrderController@checkRoboCallInitiatedOrders');
	Route::get('city/rebuild', 'CityController@rebuild');
	Route::get('courier/city/rebuild/{courier_id}/{pretend?}', 'CourierCityController@rebuild');
	Route::get('courier/area/rebuild/{courier_id}', 'CourierAreaController@rebuild');
	Route::get('shipment/track', 'ShipmentController@track');
	Route::get('old_shipment/track', 'ShipmentController@old_shipment_track');
	Route::get('shipment/track_payment', 'ShipmentController@track_payment');
	Route::get('auto_shipped', 'AutoShippedController@auto_shipped');
	Route::get('daily-updates','ReportsController@dailyUpdates');
	Route::get('courier_limit','CourierController@courier_limit');
	Route::get('fbr-daily-updates','ReportsController@fbrDailyUpdates');
	Route::get('pull-booking-data-from-sqs', 'ShipmentController@pullBookingDatafromSQS');
	Route::get('pull-qoutation-data-from-sqs', 'ShipmentController@pullQoutationsfromSQS');
	Route::get('sms-non-confirmed-orders-one-hour','SettingController@SMSNonConfirmedOrdersOneHour');
	Route::get('whatsapp-non-confirmed-orders-one-hour','SettingController@WhatsappNonConfirmedOrdersOneHour');
	Route::get('non-confirmed-orders-day-end','SettingController@NonConfirmedOrdersDayEnd');
	Route::get('un-confirmed-orders-clean-up','SettingController@unConfirmedOrdersCleanUp');

	Route::get('courier/performance-in-cities','CourierController@performanceInCities');
	Route::get('courier/performance','CourierController@performanceOverall');

	Route::get('customer/stats','CustomerController@stats');

	Route::get('ffc/change-inventory-sync', [FFCController::class,'changeInventorySync']);

	// Route::get('seller-inventory-sync/{id}','InventoryController@sellerInventorySync');
});

Route::group(['prefix' => 's-old-app'], function () {
    Route::get('/', 'ShopifyController@Auth');
    Route::get('/callback', 'ShopifyController@Callback');

    Route::get('/old', function (Request $request) {
        $apiKey = env('SHOPIFY_API_KEY', '');
        $apiSecret = env('SHOPIFY_API_SECRET', '');
        $scopes = 'read_products,read_orders,write_orders';
        $forwardingAddress = 'https://7020b6ef.ngrok.io'; //env('APP_URL', 'https://7020b6ef.ngrok.io'); // Replace this with your HTTPS Forwarding address
        $state = Str::random(10);
        $redirectUri = $forwardingAddress.'/shopify/callback';
        if (isset($request->shop)) {
            $installUrl = 'https://'.$request->shop.'/admin/oauth/authorize?client_id='.$apiKey.'&scope='.$scopes.'&state='.$state.'&redirect_uri='.$redirectUri;
            Cookie::queue('state', $state);

            return redirect($installUrl);
        } else {
            die('Bad Parameters!');
        }
    });
    Route::get('/callback-old', function (Request $request) {
        $apiKey = env('SHOPIFY_API_KEY', '');
        $apiSecret = env('SHOPIFY_API_SECRET', '');
        $accessToken = '';

        if ($request->state != $request->cookie('state')) {
            die('Cant verify');
        }
        $accessTokenRequestUrl = 'http://webhook.site/4086f374-0bb2-4a52-906a-c60de85fe1cd';
        $accessTokenRequestUrl = 'https://'.$request->shop.'/admin/oauth/access_token';

        $accessTokenPayload = [
            'client_id' => $apiKey,
            'client_secret' => $apiSecret,
            'code' => $request->code, ];
        $client = new GuzzleHttp\Client();
        $r = $client->request('POST', $accessTokenRequestUrl, [
                'json' => $accessTokenPayload, ]);

        $response = json_decode($r->getBody());
        $accessToken = $response->access_token;
        die($accessToken);
        $authClient = new GuzzleHttp\Client(['headers' => ['X-Shopify-Access-Token' => $accessToken]]);
        $response = $authClient->request('GET', 'https://'.$request->shop.'/admin/shop.json');
        die($response->getBody());

        //$generatedHash = hash_hmac('sha256', 'hello, world!', 'mykey');
    });
    Route::get('/delete-webhook/{token}/{shop}/{webhook_id}', 'ShopifyController@deleteWebhook');

});


/// APP

Route::group(['prefix' => 's-app', 'middleware' => 'spappauth'], function () {

	Route::get('/', 'ShopifyController@index');
	Route::get('callback', 'ShopifyController@appCallback');
	Route::get('sync-orders', 'ShopifyController@syncOrders');
	
});


Route::group(['prefix' => 'foree'], function() {
	Route::get('checkout', 'PaymentController@checkout');
});


Route::group(['prefix' => 'paymob'], function() {
	Route::get('checkout', 'PaymentController@paymentDetailsPaymobRuntime');
});

Route::group(['prefix' => 'swich'], function() {
	Route::get('checkout', 'PaymentController@paymentDetailsSwichRuntime');
});

Route::group(['prefix' => 'swich'], function() {
	Route::get('checkout/failure', 'PaymentController@paymentDetailsSwichFailure');
});

Route::get('/download-report', [ReportsController::class, 'downloadReport']);



