<?php

namespace App\Service\Integration\ERP;

use App\Models\OrderItem;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ERPForSAPService
{
    protected $http_client;

    public function __construct()
    {
        $this->http_client = new Client([
            'base_uri' => env('ERP_SAP_API_URL'),
            'timeout'  => 10.0,
        ]);
    }

    public function postSalesOrder(array $order_data)
    {
        try {
            $username = env('ERP_TECHNOSYS_USERNAME');
            $password = env('ERP_TECHNOSYS_PASSWORD');
            $authHeader = 'Basic ' . base64_encode($username . ':' . $password);

            $hearders_info = [
                'TS-AppKey' => env('ERP_TECHNOSYS_APP_KEY'),
                'Authorization' => $authHeader,
                'Content-Type' => 'application/json',
            ];

            $response = $this->http_client->post('TSBE/EStore/Order', [
                'headers' => [
                    'TS-AppKey' => env('ERP_TECHNOSYS_APP_KEY'),
                    'Authorization' => $authHeader,
                    'Content-Type' => 'application/json',
                ],
                'json' => $order_data,
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];
        } catch (\Exception $exception) {
            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];
        }
    }
}
