<?php

namespace App\Service\Integration\ERP;

use GuzzleHttp\Client;

class ERPForSAPService
{
    protected $http_client;

    public function __construct()
    {
        $this->http_client = new Client([
            'base_uri' => env('ERP_SAP_API_URL'),
            'timeout'  => 10.0,
        ]);
    }

    public function postSalesOrder(array $order_data)
    {
        try {
            // SAP API credentials
            $sapDBName = env('ERP_SAP_DB_NAME');
            $sapUsername = env('ERP_SAP_USERNAME');
            $sapPassword = env('ERP_SAP_PASSWORD');

            // Build the endpoint URL with query parameters
            $endpoint = 'api/SAP/SaleOrder?' . http_build_query([
                'SAPDBName' => $sapDBName,
                'SAPUsername' => $sapUsername,
                'SAPPassword' => $sapPassword,
            ]);

            $response = $this->http_client->post($endpoint, [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => $order_data,
            ]);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($response->getBody()->getContents(), true),
            ];
        } catch (\Exception $exception) {
            return [
                'success' => false,
                'status_code' => $exception instanceof \GuzzleHttp\Exception\RequestException
                    ? ($exception->getResponse() ? $exception->getResponse()->getStatusCode() : 500)
                    : 500,
                'message' => $exception->getMessage(),
            ];
        }
    }
}
