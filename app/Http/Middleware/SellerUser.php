<?php

namespace App\Http\Middleware;

use App\Models\AddOn;
use Closure;
use App\Models\SellerLocation;

class SellerUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (session('user') && \App\Models\SellerUser::where('seller_id', auth()->id())->whereId(session('user')->id)->value('activated') == 0) {
            auth()->logout();
            return redirect('/login');
        }

        $error = false;
        $allowed_routes = [
                            'seller/packaging_sheet',
                            'seller/order/print_invoice',
                            'seller/cities/search',
                            'seller/header-search',
                            'seller/check-mps',
                            'seller/get-payment-info-option',

                            'seller/shipper-advice',
                            'seller/shipper-advice/all_pending',
                            'seller/shipper-advice/all_reattempt',
                            'seller/shipper-advice/all_return',
                            'seller/shipper-advice/reattempt/decision',
                            'seller/shipper-advice/return/decision',
                            'seller/shipper-advice/return/decision',
                            'seller/rma/support-conversation',
                            'seller/rma/submit-message-reply',
                            'seller/fulfillment-orders',
                            'seller/fulfillment-orders/all-open',
                            'seller/stock-out',
                            'seller/fulfillment-orders/assign-user',
                            'seller/fulfillment-orders/reject',
                            'seller/ffc-inventory/all',
                            'seller/inventory-snaphot',
                            'seller/inventory-snaphot/show',
                            'seller/bin-wise-fulfillment-order-items',
                            'seller/report-inventory-in-hand',
                            'seller/report-fulfillment-order',
                            'seller/lowstock',
                            'seller/lowstock/show',
                            'seller/pending-putaway',
                            'seller/transfer-outbound-dump',
                            'seller/transfer-outbound-dump/show',
                            'seller/fulfillment-order-dump',
                            'seller/fulfillment-order-dump/all',
                            'seller/fulfillment-order-dump/show',
                            'seller/inbound-snapshot',
                            'seller/inbound-receiving',
                            'seller/rma',
                            'seller/trackar',
                            'seller/scan-n-ship/pack',
                            'seller/packer-performance',
                        ];

        $hold_for_processing_routes = [
                            'seller/hold-for-processing-orders',
                            'seller/hold-for-processing-datatable',
                            'seller/hold-for-processing-get-order-items',
                            'seller/order/asignLocationToOrderItems',
                            'seller/order/HFP/reSendToDOM'
                        ];

        $auto_shipped_routes = [
                            'seller/auto_shipped',
                            'seller/auto_shipped/all_log',
                            'seller/auto_shipped/get_review',
                            'seller/auto_shipped/run',
                            'seller/settings/auto_shipped',
                            'seller/settings/auto_shipped_setting'
                        ];

        $loadsheet_routes = [
                            'seller/loadsheet',
                            'seller/loadsheet/all_log',
                            'seller/shipment/print_loadsheets',
                            'seller/loadsheet/update_rider',
                            'seller/loadsheet/courier_count',
                            'seller/loadsheet/courier_count_extended'

                        ];

        $drill_reports_routes = [
                            'seller/reports/shipments/show',
                        ];

        $reports_routes = [
                            'seller/reports/cod-liability-report',
                            'seller/reports/cod-receivables-aging-report',
                            'seller/reports/cod-return-in-transit-aging-report',
                            'seller/reports/cod-return-report',
                            'seller/reports/ops-productivity-report/all',
                            'seller/reports/ops-productivity-report',
                            'seller/reports/ops-productivity-report/init',
                            'seller/reports/courier-performance-report',
                            'seller/reports/courier-performance-report/init',
                            'seller/reports/courier-performance-report/all',
                            'seller/reports/shipments/show',
                            'seller/reports/orders/show',
                            'seller/reports/reports-shipments-all',
                            'seller/reports/order-master-report',
                            'seller/reports/order-master-report/all',
                            'seller/reports/shipment-master-report',
                            'seller/reports/shipment-master-report/all',
                            'seller/reports/exception-report',
                            'seller/reports/exception-report/all',
                            'seller/reports/shipment-master-report',
                            'seller/reports/shipment-master-report/all',
                            'seller/reports/shipment-master-report/download',
                            'seller/reports/exception-report',
                            'seller/reports/last-mile-aging-report',
                            'seller/reports/last-mile-aging-report/show',
                            'seller/reports/aging-report-month-wise',
                            'seller/reports/aging-report-courier-wise',
                            'seller/reports/aging-report-day-wise',
                            'seller/reports/aging-report-time-wise',
                            'seller/reports/order-delivery-tat',
                            'seller/reports/orderDeliveryTat/show',
                            'seller/reports/last-mile-payments-reconciliation',
                            'seller/reports/last-mile-payments-reconciliation/show',
                            'seller/reports/last-mile-reconciliation-sheet',
                            'seller/reports/last-mile-reconciliation-sheet/show',
                            'seller/reports/last-mile-reconciliation-sheet/grid',
                            'seller/reports/fulfillment-velocity-report',
                            'seller/reports/fulfillment-velocity-report/show',
                            'seller/reports/courier-kpi',

                            'seller/reports/order-cancellation-analysis-report',
                            'seller/reports/order-cancellation-analysis-report/show',
                            
                            'seller/reports/shipment-status-history-report',


                            'seller/reports/sales-analysis-report',
                            'seller/reports/sales-analysis-report/show',
                            'seller/reports/order-cancellation-reasons-report',
                            'seller/reports/product-dump-report',
                            'seller/reports/courier-statuses-history-report',
                            'seller/reports/shipment-master-dump-report'
                           
                        ];

        $khaadi_reports_routes = [
                            
                        ];             

        $cod_reconciliation_routes = [
                            'seller/reports/cod_receivables',
                            'seller/reports/cod_receivables/show/{id}',
                            'seller/reports/cod_receivables/save',
                            'seller/reports/cod_receivables/show',
                            'seller/reports/cod_received',
                            'seller/reports/cod_recieved_all',
                            'seller/reports/cod_payments',
                            'seller/reports/cod_payments/store',
                            'seller/reports/cod_payments/confirmation',
                            'seller/reports/cod_payments/create',
                            'seller/reports/cod_payments/show-payments',
                        ];
                        // dd($request->path());
        if ($request->session()->has('permission')) {
            if (in_array($request->path(), $allowed_routes) || strpos($request->path(), 'dashboard')) {
            } elseif (strpos($request->path(), 'seller/settings') !== false || strpos($request->path(), 'seller/user') !== false) {
                if (session('permission')->setting == 0) {
                    $error = true;
                }
            } elseif ($request->segment(2) == 'cartons') {

                if (session('permission')->carton_view == 0) {
                    $error = true;
                } elseif ($request->segment(2) == 'create' && session('permission')->carton_create == 0) {
                    $error = true;
                } elseif ($request->segment(2) == 'toggle-status' && session('permission')->carton_edit == 0) {
                    $error = true;
                } elseif ($request->method() == 'PUT' && session('permission')->carton_edit == 0) {
                    $error = true;
                } elseif ($request->method() == 'DELETE' && session('permission')->carton_delete == 0) {
                    $error = true;
                }

            } elseif (strpos($request->path(), 'reports/cod-liability-report') !== false || strpos($request->path(), 'reports/cod-receivables-aging-report') !== false || strpos($request->path(), 'reports/cod-return-in-transit-aging-report') !== false || strpos($request->path(), 'reports/cod-return-report') !== false) {
                if (session('permission')->reports == 0) {
                    $error = true;
                }
            }  elseif (strpos($request->path(), 'seller/inventory/seller_locations') !== false ) {
                    if (session('permission')->ffc_location_create == 0) {
                        $error = true;
                    }
            } elseif ($request->route('order')) {
                if ($request->path() == 'seller/order/'.$request->route('order')) {
                    if (session('permission')->order_view == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/order/'.$request->route('order')->id) {
                    if (session('permission')->order_edit == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/order/'.$request->route('order')->id.'/edit') {
                    if (session('permission')->order_edit == 0) {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            } elseif ($request->route('order_id')) {
                if ($request->path() == 'seller/order/tagRemove/'.$request->route('order_id')) {
                    if (session('permission')->order_tagging == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/order/tag/'.$request->route('order_id').'/'.$request->route('tag')) {
                    if (session('permission')->order_tagging == 0) {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            } elseif ($request->route('auto_shipped')) {
                if ($request->path() == 'seller/settings/auto_shipped/'.$request->route('auto_shipped')->id.'/edit') {
                    if (session('permission')->auto_shipped == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/settings/auto_shipped/'.$request->route('auto_shipped')->id) {
                    if (session('permission')->auto_shipped == 0) {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/tagAdd/multiple') {
                if (session('permission')->order_tagging == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/tagRemove/multiple') {
                if (session('permission')->order_tagging == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/changeCity/multiple') {
                if (session('permission')->order_edit == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/changeLocation/multiple') {
                if (session('permission')->order_edit == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/export-order') {
                if (session('permission')->order_edit == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/bulk_cancellation'.$request->route('id')) {
                if (session('permission')->bulk_order_cancellation == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order' && $request->method() == 'GET') {
                if (session('permission')->order_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/all_pending' || $request->path() == 'seller/order/all_fulfilled' || $request->path() == 'seller/order/all_cancelled' || $request->path() == 'seller/order/all_main') {
                if (session('permission')->order_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order' && $request->method() == 'POST') {
                if (session('permission')->order_create == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/create') {
                if (session('permission')->order_create == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment' && $request->method() == 'POST') {
                if (session('permission')->order_process == 0) {
                    $error = true;
                }
            }

            else if ($request->path() == 'seller/order/getQuote' && $request->method() == 'POST') {
                if (session('permission')->order_process == 0) {
                    $error = true;
                }
            }

            elseif ($request->path() == 'seller/order/cancelItems' && $request->method() == 'POST') {
                if (session('permission')->order_cancel == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/order/cancelledOrder') {
                if (session('permission')->order_cancel == 0) {
                    $error = true;
                }
            }  elseif ($request->path() == 'seller/order/HFP/reSendToDOM' || $request->path() == 'seller/order/reSendToDOM/multiple') {
                if (session('permission')->order_edit == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/reverse-shipment/delivered/'.$request->id) {
                if (session('permission')->view_reverse_shipment == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/reverse-shipment/returned/'.$request->id) {
                if (session('permission')->view_reverse_shipment == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/product/conflicts' || $request->path() == 'seller/product/conflicts/all') {
                if (session('permission')->product_conflict_view == 0) {
                    $error = true;
                }
            } elseif ($request->route('id')) {
                if ($request->path() == 'seller/order/dispatch/'.$request->route('id').'/0' || $request->path() == 'seller/order/dispatch/'.$request->route('id').'/1') {
                    if (session('permission')->order_process == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/product/view/'.$request->route('id')) {
                    if (session('permission')->product_edit == 0) {
                        $error = true;
                    }
                }

                elseif ($request->path() == 'seller/loadsheet/log_entries/'.$request->route('id') || $request->path() == 'seller/loadsheet/generate_again/'.$request->route('id')) {
                    if (session('permission')->loadsheet == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/auto_shipped/log_entries/'.$request->route('id')) {
                    if (session('permission')->auto_shipped == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/shipment/cancelled/'.$request->route('id')) {
                    if (session('permission')->shipment_cancel == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/shipment/delivered/'.$request->route('id')) {
                    if (session('permission')->shipment_view == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/shipment/returned/'.$request->route('id')) {
                    if (session('permission')->shipment_view == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/order/dispatchCourierService/'.$request->route('id')) {
                    if (session('permission')->order_process == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/order/'.$request->route('id').'/add_comment') {
                    if (session('permission')->order_comments == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/order/'.$request->route('id').'/passthrough') {
                    if (session('permission')->order_edit == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/rma/return-request-data/'.$request->route('id')) {
                    if (session('permission')->rma_request == 0) {
                        $error = true;
                    }
                }
                elseif ($request->path() == 'api/robocall') {
                    if (session('permission')->robocall == 1) {
                        $error = true;
                    }
                }
                elseif ($request->path() == 'seller/reports/cod_payments/show-single-payment/'.$request->route('id') || $request->path() == 'seller/reports/cod_payments/update/'.$request->route('id') || $request->path() == 'seller/reports/cod_payments/courier_shipments_update/'.$request->route()->courier_id.'/'.$request->route()->cod_payment_id || $request->path() == 'seller/reports/cod_payments/courier_shipments_create/'.$request->route()->id || $request->path() == 'seller/reports/cod_payments/update/'.$request->route()->id || $request->path() == 'seller/reports/cod_payments/show-single-payment-shipment/'.$request->route()->id) {
                    if (session('permission')->cod_reconciliation == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/order/'.$request->route('id').'/resend-to-dom') {
                    if (session('permission')->order_edit == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/shipment/gtech-sync/'.$request->route('id')) {
                    if (session('permission')->gtech_sales_order == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/shipment/gtech-cancel-sync/'.$request->route('id')) {
                    if (session('permission')->gtech_cancellation_resync == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/shipment/erp-jdot-sales-sync/'.$request->route('id')) {
                    if (session('permission')->erp_jdot_sales_order == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/shipment/erp-jdot-return-sales-sync/'.$request->route('id')) {
                    if (session('permission')->erp_jdot_return_order == 0) {
                        $error = true;
                    }
                } elseif ($request->path() == 'seller/stock-transfer-order/gtech-transfer-order-sync/'.$request->route('id')) {
                    if (session('permission')->gtech_transfer_order == 0) {
                        $error = true;
                    }
                } elseif (strpos($request->path(), 'seller/inventory-hierarchy/committed-qty-details') !== false ) {
                    if (session('permission')->ffc_inventory_view == 0) {
                        $error = true;
                    }
                } elseif (strpos($request->path(), 'seller/inventory/committed-stock-details') !== false ) {
                    if (session('permission')->fo_report == 0) {
                        $error = true;
                    }
                } elseif (strpos($request->path(), 'seller/stock-transfer-order/cancel') !== false ) {
                    if (session('permission')->so_cancel == 0) {
                        $error = true;
                    }
                }
                
                else {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment/bulk_gtech_sync') {
                if (session('permission')->gtech_sales_order == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment/bulk_erp_jdot_sales_sync') {
                if (session('permission')->erp_jdot_sales_order == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment' && $request->method() == 'GET') {
                if (session('permission')->shipment_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment/all' || $request->path() == 'seller/shipment/all_dispatched' || $request->path() == 'seller/shipment/all_fulfilled' || $request->path() == 'seller/shipment/all_cancelled') {
                if (session('permission')->shipment_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment/bulk_deliver' || $request->path() == 'seller/shipment/bulk_return') {
                if (session('permission')->shipment_view == 0) {
                    $error = true;
                }
            } elseif ($request->route('shipment')) {
                if ($request->path() == 'seller/shipment/'.$request->route('shipment')) {
                    if (session('permission')->shipment_view == 0) {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            } elseif ($request->route('reverse_shipment')) {
                if ($request->path() == 'seller/reverse-shipment/'.$request->route('reverse_shipment')) {
                    if (session('permission')->view_reverse_shipment == 0) {
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            } elseif (strpos($request->path(), 'seller/shipment/reason') !== false) {
                if (session('permission')->shipment_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment/print_address_labels' || $request->path() == 'seller/shipment/print_sticker_labels' || $request->path() == 'seller/shipment/print_sticker_label_on_thermal' || $request->path() == 'seller/shipment/print_invoices' || $request->path() == 'seller/shipment/download_invoices' || $request->path() == 'seller/shipment/print_shipping_invoices') {
                if (session('permission')->print_address_label == 0) {
                    $error = true;
                }
            }  elseif ($request->path() == 'seller/shipment/bulk_cancellation'.$request->route('id')) {
                if (session('permission')->bulk_shipment_cancellation == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/shipment/print_shipping_invoices') {
                if (session('permission')->print_shipping_invoice == 0) {
                    $error = true;
                }
            }
            elseif ($request->path() == 'seller/reports/order-data-dump-report' || $request->path() == 'seller/reports/order-data-dump-report/generate') {
                if (session('permission')->order_data_dump_report == 0) {
                    $error = true;
                }
            }

            elseif ( strpos($request->path() , 'seller/return-received') !== false || strpos($request->path() , 'seller/reports/return-receiving-report') !== false ) {
                if (session('permission')->return_received == 0) {
                    $error = true;
                }
            }

            elseif (in_array($request->path() , $reports_routes)) {
                if (session('permission')->reports == 0) {
                    $error = true;
                }
            }

            elseif (in_array($request->path() , $hold_for_processing_routes)) {
                if (session('permission')->order_edit == 0) {
                    $error = true;
                }
            }

            // elseif (in_array($request->path() , $khaadi_reports_routes)) {
            //     if (session('permission')->reports == 0) {
            //         $error = true;
            //     } else{
            //         if (session('user')->seller_id == 1 || session('user')->seller_id == 119) {
            //             $error = false;
            //         }
            //         else{
            //             $error = true;
            //         }
            //     }
            // }

            

            elseif (in_array($request->path() , $cod_reconciliation_routes) || $request->path() == 'seller/reports/cod_payments/courier_shipments_create/'.$request->route()->id || $request->path() == 'seller/reports/cod_payments/courier_shipments_update/'.$request->route()->courier_id.'/'.$request->route()->cod_payment_id) {
                // dd('ttt');
                if (session('permission')->cod_reconciliation == 0) {
                    $error = true;
                }
            }

            elseif ($request->path() == 'seller/product' || $request->path() == 'seller/product/all' || $request->path() == 'seller/products/stock-order/search' || $request->path() == 'seller/product/list' || $request->path() == 'seller/product/grid') {
                if (session('permission')->product_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/product/store' && ! isset($request->pid)) {
                if (session('permission')->product_create == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/product/store' || $request->path() == 'seller/product/create' || $request->path() == 'seller/product/bulk_add') {
                if (session('permission')->product_create == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/product/store' && isset($request->pid)) {
                if (session('permission')->product_edit == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/product/delete') {
                if (session('permission')->product_delete == 0) {
                    $error = true;
                }
            } elseif (in_array($request->path(), $auto_shipped_routes)) {
                if (session('permission')->auto_shipped == 0) {
                    $error = true;
                }
            } elseif (strpos($request->path(), 'seller/auto_shipped/cancel/') !== false || strpos($request->path(), 'seller/distributed_system') !== false) {
                if (session('permission')->auto_shipped == 0) {
                    $error = true;
                }
            } elseif (in_array($request->path(), $loadsheet_routes)) {
                if (session('permission')->loadsheet == 0) {
                    $error = true;
                }
            } elseif (in_array($request->path(), $drill_reports_routes)) {
                if (session('permission')->shipment_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/reverse-shipment') {
                if (session('permission')->view_reverse_shipment == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/reverse-shipment/create') {
                if (session('permission')->create_reverse_shipment == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/settings/rma-settings') {
                if (session('permission')->rma_settings == 0) {
                    $error = true;
                }
            }
            elseif ($request->path() == 'seller/settings/timezone') {
                $error = false;
            }
            elseif ($request->path() == 'seller/rma') {
                if (session('permission')->rma_request == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/rma/return-requests') {
                if (session('permission')->rma_request == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/rma/save-request') {
                if (session('permission')->rma_request == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/rma/decision-revert') {
                if (session('permission')->rma_request == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/settings/save-deadline') {
                if (session('permission')->rma_request == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/settings/save-policy-message') {
                if (session('permission')->rma_request == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/reverse-shipment') {
                if (session('permission')->view_reverse_shipment == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/reverse-shipment/all_dispatched' || $request->path() == 'seller/reverse-shipment/all_fulfilled' || $request->path() == 'seller/reverse-shipment/all_cancelled') {
                if (session('permission')->view_reverse_shipment == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/get-credits' && AddOn::wallet(auth()->id())) {
                $error = false;
            } elseif ($request->path() == 'seller/stock-order/create' || $request->path() == 'seller/stock-transfer-order/create') {
                if (session('permission')->so_create == 0) {
                    $error = true;
                }
            } elseif (strpos($request->path(), 'seller/stock-order') !== false || strpos($request->path(), 'seller/stock-transfer-order') !== false ) {
                if (session('permission')->so_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/location-hierarchy') {
                if (session('permission')->ffc_location_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/location-hierarchy/create') {
                if (session('permission')->ffc_location_create == 0) {
                    $error = true;
                }
            } elseif ($request->route('location_hierarchy') && $request->path() == 'seller/location-hierarchy/'.$request->route('location_hierarchy')->id.'/edit') {
                if (session('permission')->ffc_location_update == 0) {
                    $error = true;
                }
            } elseif ($request->route('location_hierarchy') && $request->path() == 'seller/location-hierarchy/'.$request->route('location_hierarchy')->id) {
                if (session('permission')->ffc_location_update == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/inventory-hierarchy') {
                if (session('permission')->ffc_inventory_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/inventory-hierarchy/create') {
                if (session('permission')->ffc_inventory_create == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/inventory/bulk_add' || $request->path() == 'seller/inventory/stock_in_strategy_update') {
                if (session('permission')->inventory_bulk_upload == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/inventory' || $request->path() == 'seller/inventory/all' || $request->path() == 'seller/reports/ledger-inventory-report' || $request->path() == 'seller/reports/ledger-inventory-report-all' || $request->path() == 'seller/reports/ffc-inventory-report' || $request->path() == 'seller/reports/ffc-inventory-report-all') {
                if (session('permission')->fo_report == 0) {
                    $error = true;
                }
            } elseif (strpos($request->path(), 'replenishment') !== false ) {
                if (session('permission')->fo_report == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/pick-pack/active-pickers') {
                if (session('permission')->packing_desk_view == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/pick-pack/release-picklist' || $request->path() == 'seller/pick-pack/release-picklist-order') {
                if (session('permission')->packing_desk_release == 0) {
                    $error = true;
                }
            } elseif (strpos($request->path(), 'seller/pick-pack') !== false || strpos($request->path(), 'seller/pick-pack/transfer-order') !== false) {
                if (session('permission')->packing_desk_pack == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/inventory-hierarchy/'.$request->route('inventory-hierarchy').'/edit') {
                if (session('permission')->ffc_inventory_update == 0) {
                    $error = true;
                }
            } elseif ($request->path() == 'seller/scan-n-ship') {
                if (session('permission')->scan_n_ship == 0) {
                    $error = true;
                }
            } else {
                $error = true;
            }

            if($request->path() == 'seller/shipment/force-track/'.$request->route('id')) {
                $error = false;
            }

            if($request->path() == 'seller/stock-out/'.$request->route('location_id')) {
                $error = false;
            }

            if($request->path() == 'seller/pending-putaway/'.$request->route('location_id')) {
                $error = false;
            }

            if($request->path() == 'seller/fulfillment-orders/all-open/'.$request->route('location_id')) {
                $error = false;
            }
            
            if($request->path() == 'seller/ffc-inventory/all/'.$request->route('location_id')) {
                $error = false;
            }
        }
        if ($error) {
            return redirect('seller/order')->with('error', 'You don\'t have permission to access that page. <code>'.$request->path().'</code>');
        } else {
            return $next($request);
        }
    }
}
