<?php

namespace App\Http\Controllers;

use App\Helpers\UnauthorizedUserMessage;
use App\Models\AddOn;
use App\Models\Courier\City;
use App\Models\Seller;
use App\Models\SellerLocation;
use App\Models\SellerUser;
use App\Models\SellerUserPermission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SellerUserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data['page_title'] = 'Sub Users';
        $data['result'] = SellerUser::where('seller_id', Auth::id())->get();
        $data['all'] = count($data['result']);
        $data['enabled'] = SellerUser::where('seller_id', Auth::id())->where('activated', '1')->count();
        $data['disabled'] = SellerUser::where('seller_id', Auth::id())->where('activated', '0')->count();

        return view('seller.user.home', $data);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (AddOn::location(Auth::id())) {
            $data['locations'] = SellerLocation::where('seller_id', Auth::id())->get();
        } else {
            $data['locations'] = [];
        }

        $data['page_title'] = 'Sub Users';
        $data['add_on_erp_jdot'] = AddOn::erpJdot(Auth::id());
        $data['add_on_erp_jdot_temp'] = AddOn::erpJdotTemp(Auth::id());

        return view('seller.user.create', $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (SellerUser::where('email', $request->email)->exists()) {
            return back()->with('error', 'Email Already Exists');
        } elseif (Seller::where('email', $request->email)->exists()) {
            return back()->with('error', 'Email Already Exists');
        }
        $user = new SellerUser;
        $user->seller_id = Auth::id();
        $user->full_name = $request->full_name;
        $user->phone = $request->phone;
        $user->cnic = $request->cnic;
        $user->email = $request->email;
        $user->password = hash('sha256', $request->password);
        $user->activated = ($request->activated ? 1 : 0);
        ($request->cities ? ($user->city = implode(',', $request->cities)) : '');
        ($request->locations ? ($user->location = implode(',', $request->locations)) : '');
        ($request->order_pickup_locations ? ($user->order_pickup_location = implode(',', $request->order_pickup_locations)) : '');
        $user->save();

        $permission = new SellerUserPermission;
        $permission->seller_user_id = $user->id;
        $permission->order_create = ( $request->order_create ? 1 : 0 );
        $permission->order_view = ( $request->order_view ? 1 : 0 );
        $permission->order_edit = ( $request->order_edit ? 1 : 0 );
        $permission->order_process = ( $request->order_process ? 1 : 0 );
        $permission->order_cancel = ( $request->order_cancel ? 1 : 0 );
        $permission->shipment_view = ( $request->shipment_view ? 1 : 0 );
        $permission->shipment_cancel = ( $request->shipment_cancel ? 1 : 0 );
        $permission->product_view = ( $request->product_view ? 1 : 0 );
        $permission->product_create = ( $request->product_create ? 1 : 0 );
        $permission->product_edit = ( $request->product_edit ? 1 : 0 );
        $permission->product_delete = ( $request->product_delete ? 1 : 0 );
        $permission->product_conflict_view = ( $request->product_conflict_view ? 1 : 0 );
        $permission->product_view_marketplace_id = ( $request->product_view_marketplace_id ? 1 : 0 );
        $permission->product_edit_marketplace_id = ( $request->product_edit_marketplace_id ? 1 : 0 );
        $permission->carton_view = ( $request->carton_view ? 1 : 0 );
        $permission->carton_create = ( $request->carton_create ? 1 : 0 );
        $permission->carton_edit = ( $request->carton_edit ? 1 : 0 );
        $permission->carton_delete = ( $request->carton_delete ? 1 : 0 );
        $permission->print_address_label = ( $request->print_address_label ? 1 : 0 );
        $permission->auto_shipped = ( $request->auto_shipped ? 1 : 0 );
        $permission->loadsheet = ( $request->loadsheet ? 1 : 0 );
        $permission->rma_request = ( $request->rma_request ? 1 : 0 );
        $permission->rma_settings = ( $request->rma_settings ? 1 : 0 );
        $permission->view_reverse_shipment = ( $request->view_reverse_shipment ? 1 : 0 );
        $permission->create_reverse_shipment = ( $request->create_reverse_shipment ? 1 : 0 );
        $permission->cod_reconciliation = ( $request->cod_reconciliation ? 1 : 0 );
        $permission->reports = ( $request->reports ? 1 : 0 );
        $permission->print_shipping_invoice = ( $request->print_shipping_invoice ? 1 : 0 );
        $permission->bulk_order_cancellation = ( $request->bulk_order_cancellation ? 1 : 0 );
        $permission->bulk_shipment_cancellation = ( $request->bulk_shipment_cancellation ? 1 : 0 );
        $permission->robocall = ( $request->robocall ? 1 : 0 );
        $permission->setting = ( $request->setting ? 1 : 0 );
        $permission->order_data_dump_report = ( $request->order_data_dump_report ? 1 : 0 );
        $permission->fo_edit = ( $request->fo_edit ? 1 : 0 );
        $permission->fo_report = ( $request->fo_report ? 1 : 0 );
        $permission->so_view = ( $request->so_view ? 1 : 0 );
        $permission->so_create = ( $request->so_create ? 1 : 0 );
        $permission->so_cancel = ( $request->so_cancel ? 1 : 0 );
        $permission->ffc_location_view = ( $request->ffc_location_view ? 1 : 0 );
        $permission->ffc_location_create = ( $request->ffc_location_create ? 1 : 0 );
        $permission->ffc_location_update = ( $request->ffc_location_update ? 1 : 0 );
        $permission->ffc_inventory_view = ( $request->ffc_inventory_view ? 1 : 0 );
        $permission->ffc_inventory_create = ( $request->ffc_inventory_create ? 1 : 0 );
        $permission->ffc_inventory_update = ( $request->ffc_inventory_update ? 1 : 0 );
        $permission->ffc_inventory_dump_view = ( $request->ffc_inventory_dump_view ? 1 : 0 );
        $permission->inventory_bulk_upload = ( $request->inventory_bulk_upload ? 1 : 0 );
        $permission->ffc_app_login = ( $request->ffc_app_login ? 1 : 0 );
        $permission->ffc_app_security = ( $request->ffc_app_security ? 1 : 0 );
        $permission->ffc_app_putaway_inbound = ( $request->ffc_app_putaway_inbound ? 1 : 0 );
        $permission->ffc_app_picking = ( $request->ffc_app_picking ? 1 : 0 );
        $permission->ffc_app_item_counting_mode = ( $request->ffc_app_item_counting_mode ? 1 : 0 );
        $permission->packing_desk_view = ( $request->packing_desk_view ? 1 : 0 );
        $permission->packing_desk_pack = ( $request->packing_desk_pack ? 1 : 0 );
        $permission->packing_desk_release = ( $request->packing_desk_release ? 1 : 0 );
        $permission->return_received = ( $request->return_received ? 1 : 0 );
        $permission->data_privacy_view_pii = ( $request->data_privacy_view_pii ? 1 : 0 );
        $permission->email_technical_support = ( $request->email_technical_support ? 1 : 0 );
        $permission->email_shipper_advice = ( $request->email_shipper_advice ? 1 : 0 );
        $permission->email_daily_summary = ( $request->email_daily_summary ? 1 : 0 );
        $permission->email_order_data_dump_report = ( $request->email_order_data_dump_report ? 1 : 0 );
        $permission->scan_n_ship = ( $request->scan_n_ship ? 1 : 0 );
        $permission->cod_switch = ( $request->cod_switch ? 1 : 0 );
        $permission->order_comments = ( $request->order_comments ? 1 : 0 );
        $permission->order_tagging = ( $request->order_tagging ? 1 : 0 );
        $permission->gtech_resyncing = ( $request->gtech_resyncing ? 1 : 0 );
        $permission->gtech_transfer_order = ( $request->gtech_transfer_order ? 1 : 0 );
        $permission->gtech_sales_order = ( $request->gtech_sales_order ? 1 : 0 );
        $permission->gtech_cancellation_resync = ( $request->gtech_cancellation_resync ? 1 : 0 );
        $permission->gtech_inventory_fetch = ( $request->gtech_inventory_fetch ? 1 : 0 );
        $permission->erp_jdot_resyncing = ($request->erp_jdot_resyncing ? 1 : 0);
        $permission->erp_jdot_sales_order = ($request->erp_jdot_sales_order ? 1 : 0);
        $permission->erp_jdot_return_order = ($request->erp_jdot_return_order ? 1 : 0);
        $permission->technosys_resyncing = ( $request->technosys_resyncing ? 1 : 0 );
        $permission->technosys_transfer_order = ( $request->technosys_transfer_order ? 1 : 0 );
        $permission->technosys_sales_order = ( $request->technosys_sales_order ? 1 : 0 );
        $permission->technosys_cancellation_resync = ( $request->technosys_cancellation_resync ? 1 : 0 );

        $permission->save();

        return redirect('seller/user')->with('status', 'User  '.$request->full_name.' Created!');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(SellerUser $user)
    {
        $permission = SellerUserPermission::where('seller_user_id', $user->id)->first();
        $data['page_title'] = 'Sub User Edit';
        $data['user'] = $user;
        $data['user']->city = City::whereIn('id', explode(',', $data['user']->city))->get();

        if(isset($user) && $user->seller_id != Auth::id()){
            return UnauthorizedUserMessage::show();
        }
        if (AddOn::location(Auth::id())) {
            $data['locations'] = SellerLocation::where('seller_id', Auth::id())->get();
        } else {
            $data['locations'] = [];
        }

        $data['permission'] = $permission;
        $data['add_on_erp_jdot'] = AddOn::erpJdot(Auth::id());
        $data['add_on_erp_jdot_temp'] = AddOn::erpJdotTemp(Auth::id());
        
        return view('seller.user.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SellerUser $user)
    {
        $user->full_name = $request->full_name;
        $user->phone = $request->phone;
        $user->cnic = $request->cnic;
        $user->email = $request->email;
        if ($request->password) {
            $user->password = hash('sha256', $request->password);
        }
        $user->city = ($request->cities ? implode(',', $request->cities) : '');
        $user->location = ($request->locations ? implode(',', $request->locations) : '');
        $user->order_pickup_location = ($request->order_pickup_locations ? implode(',', $request->order_pickup_locations) : '');
        $user->activated = ($request->activated ? 1 : 0);
        $user->update();

        $permission = SellerUserPermission::where('seller_user_id',$user->id)->first();
        $permission->order_create = ( $request->order_create ? 1 : 0 );
        $permission->order_view = ( $request->order_view ? 1 : 0 );
        $permission->order_edit = ( $request->order_edit ? 1 : 0 );
        $permission->order_process = ( $request->order_process ? 1 : 0 );
        $permission->order_cancel = ( $request->order_cancel ? 1 : 0 );
        $permission->shipment_view = ( $request->shipment_view ? 1 : 0 );
        $permission->shipment_cancel = ( $request->shipment_cancel ? 1 : 0 );
        $permission->product_view = ( $request->product_view ? 1 : 0 );
        $permission->product_create = ( $request->product_create ? 1 : 0 );
        $permission->product_edit = ( $request->product_edit ? 1 : 0 );
        $permission->product_delete = ( $request->product_delete ? 1 : 0 );
        $permission->product_conflict_view = ( $request->product_conflict_view ? 1 : 0 );
        $permission->product_view_marketplace_id = ( $request->product_view_marketplace_id ? 1 : 0 );
        $permission->product_edit_marketplace_id = ( $request->product_edit_marketplace_id ? 1 : 0 );
        $permission->carton_view = ( $request->carton_view ? 1 : 0 );
        $permission->carton_create = ( $request->carton_create ? 1 : 0 );
        $permission->carton_edit = ( $request->carton_edit ? 1 : 0 );
        $permission->carton_delete = ( $request->carton_delete ? 1 : 0 );
        $permission->print_address_label = ( $request->print_address_label ? 1 : 0 );
        $permission->auto_shipped = ( $request->auto_shipped ? 1 : 0 );
        $permission->loadsheet = ( $request->loadsheet ? 1 : 0 );
        $permission->rma_request = ( $request->rma_request ? 1 : 0 );
        $permission->rma_settings = ( $request->rma_settings ? 1 : 0 );
        $permission->view_reverse_shipment = ( $request->view_reverse_shipment ? 1 : 0 );
        $permission->create_reverse_shipment = ( $request->create_reverse_shipment ? 1 : 0 );
        $permission->cod_reconciliation = ( $request->cod_reconciliation ? 1 : 0 );
        $permission->reports = ( $request->reports ? 1 : 0 );
        $permission->print_shipping_invoice = ( $request->print_shipping_invoice ? 1 : 0 );
        $permission->bulk_order_cancellation = ( $request->bulk_order_cancellation ? 1 : 0 );
        $permission->bulk_shipment_cancellation = ( $request->bulk_shipment_cancellation ? 1 : 0 );
        $permission->robocall = ( $request->robocall ? 1 : 0 );
        $permission->setting = ( $request->setting ? 1 : 0 );
        $permission->order_data_dump_report = ( $request->order_data_dump_report ? 1 : 0 );
        $permission->fo_edit = ( $request->fo_edit ? 1 : 0 );
        $permission->fo_report = ( $request->fo_report ? 1 : 0 );
        $permission->so_view = ( $request->so_view ? 1 : 0 );
        $permission->so_create = ( $request->so_create ? 1 : 0 );
        $permission->so_cancel = ( $request->so_cancel ? 1 : 0 );
        $permission->ffc_location_view = ( $request->ffc_location_view ? 1 : 0 );
        $permission->ffc_location_create = ( $request->ffc_location_create ? 1 : 0 );
        $permission->ffc_location_update = ( $request->ffc_location_update ? 1 : 0 );
        $permission->ffc_inventory_view = ( $request->ffc_inventory_view ? 1 : 0 );
        $permission->ffc_inventory_create = ( $request->ffc_inventory_create ? 1 : 0 );
        $permission->ffc_inventory_update = ( $request->ffc_inventory_update ? 1 : 0 );
        $permission->ffc_inventory_dump_view = ( $request->ffc_inventory_dump_view ? 1 : 0 );
        $permission->inventory_bulk_upload = ( $request->inventory_bulk_upload ? 1 : 0 );
        $permission->ffc_app_login = ( $request->ffc_app_login ? 1 : 0 );
        $permission->ffc_app_security = ( $request->ffc_app_security ? 1 : 0 );
        $permission->ffc_app_putaway_inbound = ( $request->ffc_app_putaway_inbound ? 1 : 0 );
        $permission->ffc_app_picking = ( $request->ffc_app_picking ? 1 : 0 );
        $permission->ffc_app_item_counting_mode = ( $request->ffc_app_item_counting_mode ? 1 : 0 );
        $permission->packing_desk_view = ( $request->packing_desk_view ? 1 : 0 );
        $permission->packing_desk_pack = ( $request->packing_desk_pack ? 1 : 0 );
        $permission->packing_desk_release = ( $request->packing_desk_release ? 1 : 0 );
        $permission->return_received = ( $request->return_received ? 1 : 0 );
        $permission->data_privacy_view_pii = ( $request->data_privacy_view_pii ? 1 : 0 );
        $permission->email_technical_support = ( $request->email_technical_support ? 1 : 0 );
        $permission->email_shipper_advice = ( $request->email_shipper_advice ? 1 : 0 );
        $permission->email_daily_summary = ( $request->email_daily_summary ? 1 : 0 );
        $permission->email_order_data_dump_report = ( $request->email_order_data_dump_report ? 1 : 0 );
        $permission->scan_n_ship = ( $request->scan_n_ship ? 1 : 0 );
        $permission->cod_switch = ( $request->cod_switch ? 1 : 0 );
        $permission->order_comments = ( $request->order_comments ? 1 : 0 );
        $permission->order_tagging = ( $request->order_tagging ? 1 : 0 );
        $permission->gtech_resyncing = ( $request->gtech_resyncing ? 1 : 0 );
        $permission->gtech_transfer_order = ( $request->gtech_transfer_order ? 1 : 0 );
        $permission->gtech_sales_order = ( $request->gtech_sales_order ? 1 : 0 );
        $permission->gtech_cancellation_resync = ( $request->gtech_cancellation_resync ? 1 : 0 );
        $permission->gtech_inventory_fetch = ( $request->gtech_inventory_fetch ? 1 : 0 );
        $permission->erp_jdot_resyncing = ($request->erp_jdot_resyncing ? 1 : 0);
        $permission->erp_jdot_sales_order = ($request->erp_jdot_sales_order ? 1 : 0);
        $permission->erp_jdot_return_order = ($request->erp_jdot_return_order ? 1 : 0);
        $permission->technosys_resyncing = ( $request->technosys_resyncing ? 1 : 0 );
        $permission->technosys_transfer_order = ( $request->technosys_transfer_order ? 1 : 0 );
        $permission->technosys_sales_order = ( $request->technosys_sales_order ? 1 : 0 );
        $permission->technosys_cancellation_resync = ( $request->technosys_cancellation_resync ? 1 : 0 );
        $permission->update();

        return redirect('seller/user')->with('status', 'User  '.$request->full_name.' Updated!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
