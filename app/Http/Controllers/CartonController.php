<?php

namespace App\Http\Controllers;

use App\Models\Carton;
use App\Models\CartonProduct;
use DB;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Yajra\DataTables\DataTables;

class CartonController extends Controller
{
    public function index()
    {
        $page_title = 'Cartons Management';
        $cartons = Carton::forSeller(auth()->id())->groupBy('status')->get(['status', DB::raw('count(*) as total') ]);
        $summary = [
            'total' => $cartons->count(),
            'active' => $cartons->where('status', 1)->sum('total'),
            'inactive' => $cartons->where('status', 0)->sum('total'),
        ];
        return view('seller.carton.index', compact('cartons', 'summary', 'page_title'));
    }

    public function getAll()
    {
        return DataTables::of(Carton::forSeller(2))
            ->editColumn('created_at', function ($carton) {
                return $carton->created_at->toDayDateTimeString();
            })->editColumn('updated_at', function ($carton) {
                return $carton->updated_at->toDayDateTimeString();
            })->make(true);
    }

    public function show(Carton $carton)
    {
        $carton->load('cartonProducts');
        return view('seller.carton.show', compact('carton'));
    }

    public function create()
    {
        $page_title = 'Create Carton';
        return view('seller.carton.create', compact('page_title'));
    }

    public function store(Request $request)
    {
        $request->products = json_decode($request->products, true);

        $data = $request->validate([
            'name' => 'required|string|max:255',
            'barcode' => 'required|string|max:255|unique:cartons,barcode',
            'products.*.id' => 'required|integer|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
        ]);

        $carton = new Carton();
        $carton->seller_id = auth()->id();
        $carton->name = $request->name;
        $carton->barcode = $request->barcode;
        $carton->save();

        $total_products = 0;
        $total_quantity = 0;

        foreach ($request->products as $product) {

            $carton_product = new CartonProduct();
            $carton_product->carton_id = $carton->id;
            $carton_product->product_id = $product['id'];
            $carton_product->quantity = $product['quantity'];
            $carton_product->save();

            $total_products++;
            $total_quantity += $product['quantity'];
        }

        $carton->total_product = $total_products;
        $carton->total_quantity = $total_quantity;
        $carton->save();

        return redirect()->route('cartons.index');
    }

    public function edit(Carton $carton)
    {
        $page_title = 'Edit Carton';
        $carton_products = $carton->cartonProducts()->with('product:id,product_name,barcode,SKU')->get();
        return view('seller.carton.edit', compact('page_title', 'carton', 'carton_products'));
    }

    public function update(Request $request, Carton $carton)
    {
        $request->products = json_decode($request->products, true);
        
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'barcode' => 'required|string|max:255|unique:cartons,barcode,' . $carton->id,
            'products.*.product_id' => 'required|integer|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
        ]);
        
        $carton->name = $request->name;
        $carton->barcode = $request->barcode;
        $carton->save();

        $total_products = 0;
        $total_quantity = 0;

        CartonProduct::where('carton_id', $carton->id)->delete();

        foreach ($request->products as $product) {

            $carton_product = new CartonProduct();
            $carton_product->carton_id = $carton->id;
            $carton_product->product_id = $product['id'];
            $carton_product->quantity = $product['quantity'];
            $carton_product->save();

            $total_products++;
            $total_quantity += $product['quantity'];
        }

        $carton->total_product = $total_products;
        $carton->total_quantity = $total_quantity;
        $carton->save();

        return redirect()->route('cartons.index')->with('status', 'Carton updated successfully');
    }

    public function destroy(Carton $carton)
    {
        $carton->delete();
        CartonProduct::where('carton_id', $carton->id)->delete();
        return ['message' => 'Carton deleted successfully', 'success' => true];
    }

    public function toggleStatus()
    {
        $carton = Carton::whereId(request('carton_id'))->where('seller_id', auth()->id())->first();

        if (!$carton) {
            return response()->json(['message' => 'Carton not found', 'success' => false], Response::HTTP_NOT_FOUND);
        }

        $carton->status = $carton->status === 1 ? 0 : 1;
        $carton->save();
        return ['message' => 'Carton status updated successfully', 'success' => true];
    }
}
