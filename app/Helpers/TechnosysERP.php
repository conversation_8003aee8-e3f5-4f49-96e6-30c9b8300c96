<?php 

namespace App\Helpers;
use App\Models\{SellerLocation, FulfillmentOrder, FulfillmentOrderItem, SellerPaymentMethod, OrderComments, OrderItem, DumpErpSale, Shipment, Product, Inventory, Setting};
use App\Service\Integration\ERP\ERPForTechnosys;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TechnosysERP
{
    protected $erp_service;
    protected $grand_total;

    public function __construct(ERPForTechnosys $erp_service)
    {
        $this->erp_service = $erp_service;
    }

    public function createSalesOrder(array $params): void
    {
        $order_id = $params['internal_params']['order_id'];
        $shipment = $params['shipment'];
        $seller_id = $params['internal_params']['seller_id'];
        $order = $shipment->order;

        $fulfillment_order = FulfillmentOrder::with(['items.order_item', 'location'])->whereShipmentId($shipment->id)->firstOrFail();

        $shipping_charges = $this->getShippingCharges($fulfillment_order->id, $order);
        $seller_payment_method = SellerPaymentMethod::find($order->seller_payment_method_id);

        $payment_method_name = $seller_payment_method?->display_name ?? '';
        $sale_line_items = $this->processLineItems($fulfillment_order->items);

        $sales_payload = $this->buildSalesPayload(
            $order,
            $fulfillment_order,
            $sale_line_items,
            $shipping_charges,
            $shipment->tracking_number
        );

        $dump_erp_sale = DumpErpSale::create([
            'seller_id' => $seller_id,
            'order_id' => $order_id,
            'fulfillment_id' => $fulfillment_order->id,
            'shipment_id' => $shipment->id,
            'type' => 'sales',
            'payload' => json_encode($sales_payload),
        ]);

        $this->handleSaleOrderApiResponse($order_id, $dump_erp_sale, $sales_payload, $fulfillment_order);
    }

    private function buildSalesPayload($order, $fulfillment_order, $sale_line_items, $shipping_charges, $tracking_number): array
    {
        return [
            "EStoreOrderId" => $fulfillment_order->reference_id,
            "NetAmount" => $this->grand_total + $shipping_charges,
            "CreditcardMerchantId" => $this->getPaymentAccountID($fulfillment_order),
            "CompanyBranchId" => $fulfillment_order->location->seller_reference_id,
            "IsFinalinvoice" => true,
            "ShippingAmount" => $shipping_charges,
            "Comment" => "Sale Order for FO ID: {$fulfillment_order->reference_id}, Tracking Number: {$tracking_number}",
            "OrderClient" => [
                "EStoreClientId" => strtolower("estore-cli-" . $fulfillment_order->reference_id), // Example value, adjust as needed
                "Phone" => $order->customer_number,
                "ClientName" => $order->customer_name,
                "Address1" => $order->shipping_address,
                "Area" =>"N/A", // Example value, adjust as needed
                "City" => $order->destination_city,
                "Country" => $order->country ?? "PK", // Example value, adjust as needed
            ],
            "OrderLines" => $sale_line_items,
        ];
    }

    private function handleSaleOrderApiResponse($order_id, $dump_erp_sale, $sales_payload, $fulfillment_order): void
    {
        $response = $this->erp_service->postSalesOrder($sales_payload);
        $status = $response['status_code'];

        $recipients = explode(',', env('TECHNOSYS_NOTIFICATION_EMAIL', ''));
        $unity_email = env('UNITY_NOTIFICATION_EMAIL', '');
        $subject = "Technosys - Order ID: {$order_id} - Sales Order - Sync Failed - Status Code: {$status}";
        $send_email = false;
        Log::info('Technosys ERP Response: ' . json_encode($response));

        if ($status === 200 || $status === 201) {
            if (isset($response['success']) && $response['success'] === true) {
                $dump_erp_sale->markAsSynced(json_encode($response['data']), $status);
                $document_number = $response['data']['detail']['Id'] ?? '';
                $message = "The invoice number received from Technosys: {$document_number}";

                // Save the document number to the FulfillmentOrder
                $fulfillment_order->erp_fulfillment_id = $document_number;
                $fulfillment_order->save();

                OrderComments::add($order_id, 'ERP Sync Process', $message, 'Success', 1);
            } else {
                $dump_erp_sale->markSyncFailed(json_encode($response['data']), $status);
                $message = $response['data']['message'] ?? $response['data'];
                OrderComments::add($order_id, 'ERP Sync Process', $message, 'Failed', 1);
                $body = "An error occurred during the ERP Sync Process.\n\nResponse Data: " . json_encode($response['data'] ?? $response['data']);
                $send_email = true;
            }
        } else {
            $dump_erp_sale->markSyncFailed(json_encode($response['message']), $status);
            $message = $response['message'] ?? 'Something went wrong';
            OrderComments::add($order_id, 'ERP Sync Process', $message, 'Failed', 1);
            $body = "An error occurred during the ERP Sync Process.\n\nResponse Message: " . json_encode($response['message']);
            $send_email = true;
        }

        if ($send_email) {
            $this->sendErrorNotification($order_id, $message, $status);
        }
    }

    private function sendErrorNotification($order_id, $message, $status): void
    {
        $recipients = explode(',', env('TECHNOSYS_NOTIFICATION_EMAIL', ''));
        $unity_email = env('UNITY_NOTIFICATION_EMAIL', '');
        $subject = "Technosys - Order ID: {$order_id} - Sales Order - Sync Failed - Status Code: {$status}";
        $body = "An error occurred during the ERP Sync Process.\n\nMessage: {$message}";

        Mail::raw($body, function ($m) use ($recipients, $unity_email, $subject) {
            $m->to($recipients)
                ->bcc($unity_email)
                ->subject($subject);
        });
    }

    private function getShippingCharges($fulfillment_order_id, $order): float
    {
        return $fulfillment_order_id == $order->shipping_fee_charged ? $order->shipping_fee : 0.0;
    }

    private function processLineItems($fulfillment_order_items): array
    {
        $line_items = [];
        foreach ($fulfillment_order_items as $fulfillment_order_item) {
            $order_item = $fulfillment_order_item->order_item;

            $line_items[] = [
                "Barcode" => $order_item->barcode,
                "Quantity" => $order_item->quantity,
                "Price" => intval($order_item->sub_total / $order_item->quantity),
                "ItemComment" => $order_item->product_name, // Example value, adjust as needed
            ];

            $this->grand_total += $order_item->sub_total;
        }

        return $line_items;
    }

    private function getPaymentAccountID($fulfillment_order)
    {
        $shipment = Shipment::find($fulfillment_order->shipment_id);

        if ($shipment->cod > 0) {
            // Try to get mapping from enum.technosys_payment_account.courier_accounts
            $courier_accounts = config('enum.technosys_payment_account.courier_accounts', []);
            if (isset($courier_accounts[$shipment->courier_id])) {
                return $courier_accounts[$shipment->courier_id];
            }
            // Fallback to cod account if mapping not found
            return config('enum.technosys_payment_account.cod');
        }

        return config('enum.technosys_payment_account.default');
    }
}
